# CRFM - Caisse de Retraite des Fonctionnaires du Mali

## 📋 Description

Système de gestion des retraites pour la Caisse de Retraite des Fonctionnaires du Mali (CRFM). Cette plateforme permet la gestion complète des adhérents, cotisations, pensions et outils administratifs.

## 🚀 Fonctionnalités

### 👥 Gestion des Adhérents
- Inscription et gestion des profils
- Suivi des informations personnelles et professionnelles
- Historique des modifications

### 💰 Gestion des Cotisations
- Enregistrement des cotisations mensuelles
- Calculs automatiques
- Rapports et statistiques

### 🏦 Gestion des Pensions
- Création et suivi des dossiers de pension
- Calculs des montants
- Validation et approbation

### ⚙️ Administration
- Tableau de bord avec statistiques
- Gestion des utilisateurs
- Paramètres système
- Outils de vérification et maintenance

## 🛠️ Technologies

- **Backend**: Laravel 10.x
- **Base de données**: MySQL 8.0
- **Frontend**: Bootstrap 5, jQuery
- **Architecture**: Mo<PERSON>laire (modules séparés)

## 📦 Installation

### Prérequis
- PHP 8.1+
- Composer
- MySQL 8.0+
- Node.js (optionnel)

### Installation Rapide

```bash
# Cloner le projet
git clone [url-du-repo]
cd e-carriere

# Exécuter le script de déploiement
./deploy.sh local
```

### Installation Manuelle

```bash
# 1. Installer les dépendances
composer install

# 2. Configurer l'environnement
cp .env.example .env
php artisan key:generate

# 3. Configurer la base de données dans .env
# DB_DATABASE=crfm
# DB_USERNAME=votre_utilisateur
# DB_PASSWORD=votre_mot_de_passe

# 4. Exécuter les migrations
php artisan migrate

# 5. Charger les données de test
php artisan db:seed

# 6. Créer le lien symbolique
php artisan storage:link

# 7. Optimiser le système
php artisan optimize
```

## 🔧 Commandes Utiles

### Vérification du Système
```bash
php artisan system:check
```

### Maintenance
```bash
# Maintenance de base
php artisan system:maintenance

# Maintenance avec optimisation
php artisan system:maintenance --optimize
```

### Gestion des Paramètres
```bash
# Lister tous les paramètres
php artisan settings:manage list

# Obtenir un paramètre
php artisan settings:manage get app_name

# Définir un paramètre
php artisan settings:manage set app_name "CRFM" --type=string
```

## 👤 Comptes par Défaut

### Administrateur
- **Email**: <EMAIL>
- **Mot de passe**: Admin@CRFM2024

## 📁 Structure du Projet

```
├── app/                    # Application principale
├── modules/               # Modules métier
│   ├── Auth/             # Authentification
│   ├── Dashboard/        # Tableau de bord
│   ├── Adherents/        # Gestion des adhérents
│   ├── Cotisations/      # Gestion des cotisations
│   └── Pensions/         # Gestion des pensions
├── database/             # Migrations et seeders
├── resources/            # Vues et assets
└── storage/              # Fichiers et logs
```

## 🔒 Sécurité

- Authentification sécurisée
- Validation des données
- Protection CSRF
- Hashage des mots de passe
- Gestion des permissions

## 📊 Monitoring

### Logs
Les logs sont stockés dans `storage/logs/laravel.log`

### Statistiques
Accédez aux statistiques via le tableau de bord ou la page Outils.

## 🚀 Déploiement en Production

1. Configurer l'environnement de production dans `.env`
2. Exécuter `./deploy.sh production`
3. Configurer le serveur web (Apache/Nginx)
4. Configurer les tâches cron si nécessaire

## 🤝 Support

Pour toute question ou problème :
1. Vérifiez les logs dans `storage/logs/`
2. Exécutez `php artisan system:check`
3. Consultez la documentation

## 📝 Changelog

### Version 1.0.0
- Gestion complète des adhérents
- Système de cotisations
- Gestion des pensions
- Interface d'administration
- Outils de maintenance

## 📄 Licence

Ce projet est sous licence propriétaire - CRFM Mali.

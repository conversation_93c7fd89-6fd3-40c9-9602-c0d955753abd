<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chart.js - CRFM</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Chart.js - Page Statistiques CRFM</h1>
        
        <div class="test-section">
            <h2>1. Test de chargement de Chart.js</h2>
            <div id="chartjs-status" class="status warning">⏳ Vérification en cours...</div>
            <div id="chartjs-info"></div>
        </div>

        <div class="test-section">
            <h2>2. Test graphique simple</h2>
            <div id="simple-chart-status" class="status warning">⏳ Création du graphique...</div>
            <div class="chart-container">
                <canvas id="simpleChart"></canvas>
            </div>
        </div>

        <div class="test-section">
            <h2>3. Test graphique avec données CRFM</h2>
            <div id="crfm-chart-status" class="status warning">⏳ Création du graphique CRFM...</div>
            <div class="chart-container">
                <canvas id="crfmChart"></canvas>
            </div>
        </div>

        <div class="test-section">
            <h2>4. Console de débogage</h2>
            <div id="debug-log" class="log">Initialisation des tests...\n</div>
        </div>
    </div>

    <!-- Chargement de Chart.js -->
    <script src="files/assets/js/chart-v4.min.js"></script>
    
    <script>
        // Fonction de logging
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // Fonction pour mettre à jour le statut
        function updateStatus(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.innerHTML = message;
        }

        // Test 1: Vérification de Chart.js
        log('=== DÉBUT DES TESTS ===');
        
        setTimeout(() => {
            if (typeof Chart === 'undefined') {
                updateStatus('chartjs-status', 'error', '❌ Chart.js non chargé');
                log('❌ Chart.js non disponible');
                document.getElementById('chartjs-info').innerHTML = 
                    '<p>Chart.js n\'a pas pu être chargé. Vérifiez:</p>' +
                    '<ul>' +
                    '<li>Le fichier files/assets/js/chart-v4.min.js existe</li>' +
                    '<li>Le serveur web fonctionne</li>' +
                    '<li>Aucune erreur dans la console</li>' +
                    '</ul>';
                return;
            }

            updateStatus('chartjs-status', 'success', '✅ Chart.js chargé avec succès');
            log(`✅ Chart.js disponible, version: ${Chart.version || 'inconnue'}`);
            document.getElementById('chartjs-info').innerHTML = 
                `<p><strong>Version:</strong> ${Chart.version || 'inconnue'}</p>` +
                `<p><strong>Types disponibles:</strong> ${Object.keys(Chart.registry.controllers).join(', ')}</p>`;

            // Test 2: Graphique simple
            try {
                const simpleCanvas = document.getElementById('simpleChart');
                const simpleChart = new Chart(simpleCanvas, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai'],
                        datasets: [{
                            label: 'Test Simple',
                            data: [10, 20, 15, 25, 30],
                            borderColor: '#007bff',
                            backgroundColor: 'rgba(0, 123, 255, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                
                updateStatus('simple-chart-status', 'success', '✅ Graphique simple créé');
                log('✅ Graphique simple créé avec succès');
                
            } catch (error) {
                updateStatus('simple-chart-status', 'error', '❌ Erreur graphique simple');
                log(`❌ Erreur graphique simple: ${error.message}`);
            }

            // Test 3: Graphique avec données CRFM (simulées)
            try {
                const crfmData = {
                    labels: ['Oct 2024', 'Nov 2024', 'Dec 2024', 'Jan 2025', 'Feb 2025', 'Mar 2025'],
                    cotisations: [8500, 9200, 10100, 11000, 10800, 11700],
                    pensions: [15000, 16500, 18000, 19200, 20500, 24093]
                };

                const crfmCanvas = document.getElementById('crfmChart');
                const crfmChart = new Chart(crfmCanvas, {
                    type: 'line',
                    data: {
                        labels: crfmData.labels,
                        datasets: [{
                            label: 'Cotisations (€)',
                            data: crfmData.cotisations,
                            borderColor: '#1f77b4',
                            backgroundColor: 'rgba(31, 119, 180, 0.1)',
                            tension: 0.4
                        }, {
                            label: 'Pensions (€)',
                            data: crfmData.pensions,
                            borderColor: '#ff7f0e',
                            backgroundColor: 'rgba(255, 127, 14, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value.toLocaleString() + ' €';
                                    }
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'Évolution Cotisations vs Pensions - CRFM'
                            }
                        }
                    }
                });
                
                updateStatus('crfm-chart-status', 'success', '✅ Graphique CRFM créé');
                log('✅ Graphique CRFM créé avec succès');
                
            } catch (error) {
                updateStatus('crfm-chart-status', 'error', '❌ Erreur graphique CRFM');
                log(`❌ Erreur graphique CRFM: ${error.message}`);
            }

            log('=== TESTS TERMINÉS ===');
            
        }, 1000);

        // Capturer les erreurs JavaScript
        window.addEventListener('error', function(e) {
            log(`❌ Erreur JavaScript: ${e.message} (${e.filename}:${e.lineno})`);
        });
    </script>
</body>
</html>

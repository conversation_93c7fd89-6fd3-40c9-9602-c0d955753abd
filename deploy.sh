#!/bin/bash

# Script de déploiement CRFM
# Usage: ./deploy.sh [environment]

set -e

ENVIRONMENT=${1:-local}
PROJECT_DIR=$(pwd)

echo "🚀 Déploiement CRFM - Environnement: $ENVIRONMENT"
echo "📁 Répertoire: $PROJECT_DIR"
echo ""

# Vérification des prérequis
echo "🔍 Vérification des prérequis..."
if ! command -v php &> /dev/null; then
    echo "❌ PHP n'est pas installé"
    exit 1
fi

if ! command -v composer &> /dev/null; then
    echo "❌ Composer n'est pas installé"
    exit 1
fi

echo "✅ Prérequis OK"
echo ""

# Installation des dépendances
echo "📦 Installation des dépendances..."
composer install --no-dev --optimize-autoloader
echo "✅ Dépendances installées"
echo ""

# Configuration de l'environnement
echo "⚙️ Configuration de l'environnement..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "📝 Fichier .env créé"
fi

# Génération de la clé d'application
php artisan key:generate --force
echo "🔑 Clé d'application générée"
echo ""

# Configuration de la base de données
echo "🗄️ Configuration de la base de données..."
php artisan migrate --force
echo "✅ Migrations exécutées"

# Seeders (seulement en local/dev)
if [ "$ENVIRONMENT" = "local" ] || [ "$ENVIRONMENT" = "dev" ]; then
    echo "🌱 Exécution des seeders..."
    php artisan db:seed --force
    echo "✅ Données de test créées"
fi
echo ""

# Création du lien symbolique pour le storage
echo "🔗 Création du lien symbolique storage..."
php artisan storage:link
echo "✅ Lien symbolique créé"
echo ""

# Optimisation du système
echo "⚡ Optimisation du système..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize
echo "✅ Système optimisé"
echo ""

# Permissions
echo "🔐 Configuration des permissions..."
chmod -R 775 storage bootstrap/cache
echo "✅ Permissions configurées"
echo ""

# Vérification finale
echo "🔍 Vérification finale du système..."
php artisan system:check
echo ""

# Maintenance
echo "🔧 Maintenance du système..."
php artisan system:maintenance
echo ""

echo "🎉 Déploiement terminé avec succès !"
echo ""
echo "📋 Informations de connexion:"
echo "   URL: http://127.0.0.1:8000"
echo "   Email: <EMAIL>"
echo "   Mot de passe: Admin@CRFM2024"
echo ""
echo "🚀 Pour démarrer le serveur: php artisan serve"

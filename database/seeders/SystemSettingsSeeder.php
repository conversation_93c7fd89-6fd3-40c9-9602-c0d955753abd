<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SystemSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // Paramètres généraux
            [
                'key' => 'app_name',
                'value' => 'CRFM',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Nom de l\'application',
                'description' => 'Nom affiché dans l\'interface',
                'is_public' => true
            ],
            [
                'key' => 'organization_name',
                'value' => 'Caisse de Retraite des Fonctionnaires du Mali',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Nom de l\'organisation',
                'description' => 'Nom complet de l\'organisation',
                'is_public' => true
            ],
            [
                'key' => 'organization_address',
                'value' => 'Bamako, Mali',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Adresse de l\'organisation',
                'description' => 'Adresse physique de l\'organisation',
                'is_public' => true
            ],
            [
                'key' => 'organization_phone',
                'value' => '+223 XX XX XX XX',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Téléphone de l\'organisation',
                'description' => 'Numéro de téléphone principal',
                'is_public' => true
            ],
            [
                'key' => 'organization_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Email de l\'organisation',
                'description' => 'Adresse email principale',
                'is_public' => true
            ],

            // Paramètres d'apparence
            [
                'key' => 'theme_color',
                'value' => '#1f77b4',
                'type' => 'string',
                'group' => 'appearance',
                'label' => 'Couleur du thème',
                'description' => 'Couleur principale de l\'interface',
                'is_public' => true
            ],
            [
                'key' => 'items_per_page',
                'value' => '15',
                'type' => 'integer',
                'group' => 'appearance',
                'label' => 'Éléments par page',
                'description' => 'Nombre d\'éléments affichés par page dans les listes',
                'is_public' => false
            ],

            // Paramètres système
            [
                'key' => 'timezone',
                'value' => 'Africa/Bamako',
                'type' => 'string',
                'group' => 'system',
                'label' => 'Fuseau horaire',
                'description' => 'Fuseau horaire par défaut du système',
                'is_public' => false
            ],
            [
                'key' => 'date_format',
                'value' => 'd/m/Y',
                'type' => 'string',
                'group' => 'system',
                'label' => 'Format de date',
                'description' => 'Format d\'affichage des dates',
                'is_public' => true
            ],
            [
                'key' => 'currency',
                'value' => 'FCFA',
                'type' => 'string',
                'group' => 'system',
                'label' => 'Devise',
                'description' => 'Devise utilisée dans le système',
                'is_public' => true
            ],
            [
                'key' => 'session_timeout',
                'value' => '120',
                'type' => 'integer',
                'group' => 'system',
                'label' => 'Timeout de session (minutes)',
                'description' => 'Durée d\'inactivité avant déconnexion automatique',
                'is_public' => false
            ],

            // Paramètres de notifications
            [
                'key' => 'email_notifications',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'notifications',
                'label' => 'Notifications email',
                'description' => 'Activer les notifications par email',
                'is_public' => false
            ],
            [
                'key' => 'sms_notifications',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'notifications',
                'label' => 'Notifications SMS',
                'description' => 'Activer les notifications par SMS',
                'is_public' => false
            ],

            // Paramètres de sécurité
            [
                'key' => 'registration_enabled',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'security',
                'label' => 'Inscription ouverte',
                'description' => 'Permettre l\'inscription de nouveaux utilisateurs',
                'is_public' => false
            ],
            [
                'key' => 'maintenance_mode',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'security',
                'label' => 'Mode maintenance',
                'description' => 'Activer le mode maintenance',
                'is_public' => false
            ],
        ];

        foreach ($settings as $setting) {
            \App\Models\SystemSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('✅ Paramètres système initialisés avec succès.');
    }
}

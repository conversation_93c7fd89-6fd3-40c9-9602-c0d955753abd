@extends('layouts.template')

@section('title', 'Nouvel Utilisateur - CRFM')

@section('page-title', 'Nouvel Utilisateur')
@section('page-description', 'Créer un nouveau compte utilisateur')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('users.index') }}">Utilisateurs</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Nouveau</a></li>
    </ul>
@endsection

@section('content')
<form method="POST" action="{{ route('users.store') }}" class="needs-validation" novalidate>
    @csrf
    
    <div class="row">
        <!-- Informations personnelles -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-user text-c-blue me-2"></i>Informations personnelles</h5>
                </div>
                <div class="card-block">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Nom complet</label>
                                <input type="text" name="name" class="form-control {{ $errors->has('name') ? 'is-invalid' : '' }}"
                                       value="{{ old('name') }}" required>
                                @if($errors->has('name'))
                                    <div class="invalid-feedback">{{ $errors->first('name') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Email</label>
                                <input type="email" name="email" class="form-control {{ $errors->has('email') ? 'is-invalid' : '' }}" 
                                       value="{{ old('email') }}" required>
                                @if($errors->has('email'))
                                    <div class="invalid-feedback">{{ $errors->first('email') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Téléphone</label>
                                <input type="text" name="telephone" class="form-control {{ $errors->has('telephone') ? 'is-invalid' : '' }}" 
                                       value="{{ old('telephone') }}">
                                @if($errors->has('telephone'))
                                    <div class="invalid-feedback">{{ $errors->first('telephone') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Rôle</label>
                                <select name="role" class="form-control {{ $errors->has('role') ? 'is-invalid' : '' }}" required>
                                    <option value="">Sélectionner un rôle</option>
                                    <option value="admin" {{ old('role') == 'admin' ? 'selected' : '' }}>Administrateur</option>
                                    <option value="gestionnaire" {{ old('role') == 'gestionnaire' ? 'selected' : '' }}>Gestionnaire</option>
                                    <option value="operateur" {{ old('role') == 'operateur' ? 'selected' : '' }}>Opérateur</option>
                                </select>
                                @if($errors->has('role'))
                                    <div class="invalid-feedback">{{ $errors->first('role') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Adresse</label>
                                <textarea name="adresse" class="form-control {{ $errors->has('adresse') ? 'is-invalid' : '' }}" 
                                          rows="3">{{ old('adresse') }}</textarea>
                                @if($errors->has('adresse'))
                                    <div class="invalid-feedback">{{ $errors->first('adresse') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Paramètres de sécurité -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-shield text-c-green me-2"></i>Sécurité</h5>
                </div>
                <div class="card-block">
                    <div class="form-group">
                        <label class="required">Mot de passe</label>
                        <input type="password" name="password" class="form-control {{ $errors->has('password') ? 'is-invalid' : '' }}" required>
                        <small class="form-text text-muted">Minimum 8 caractères</small>
                        @if($errors->has('password'))
                                    <div class="invalid-feedback">{{ $errors->first('password') }}</div>
                                @endif
                    </div>
                    
                    <div class="form-group">
                        <label class="required">Confirmer le mot de passe</label>
                        <input type="password" name="password_confirmation" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" class="form-check-input" id="is_active" 
                                   value="1" {{ old('is_active', '1') ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Compte actif
                            </label>
                        </div>
                        <small class="form-text text-muted">L'utilisateur pourra se connecter</small>
                    </div>
                </div>
            </div>
            
            <!-- Informations sur les rôles -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-info text-c-blue me-2"></i>Rôles et permissions</h5>
                </div>
                <div class="card-block">
                    <div class="role-info">
                        <div class="role-item" data-role="admin">
                            <span class="badge bg-danger">Administrateur</span>
                            <small class="d-block text-muted mt-1">Accès complet au système</small>
                        </div>
                        <div class="role-item" data-role="gestionnaire">
                            <span class="badge bg-warning">Gestionnaire</span>
                            <small class="d-block text-muted mt-1">Gestion des adhérents et cotisations</small>
                        </div>
                        <div class="role-item" data-role="operateur">
                            <span class="badge bg-info">Opérateur</span>
                            <small class="d-block text-muted mt-1">Consultation et saisie limitée</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Boutons d'action -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-block text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="feather icon-save me-2"></i>Créer l'utilisateur
                    </button>
                    <a href="{{ route('users.index') }}" class="btn btn-secondary btn-lg ms-2">
                        <i class="feather icon-x me-2"></i>Annuler
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<style>
.required::after {
    content: " *";
    color: red;
}
.form-group {
    margin-bottom: 1rem;
}
.card-block {
    padding: 1.5rem;
}
.role-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}
.role-item:last-child {
    border-bottom: none;
}
.role-info {
    max-height: 200px;
    overflow-y: auto;
}
</style>
@endpush

@push('scripts')
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Affichage des informations de rôle
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.querySelector('select[name="role"]');
    const roleItems = document.querySelectorAll('.role-item');
    
    function updateRoleInfo() {
        const selectedRole = roleSelect.value;
        roleItems.forEach(item => {
            if (item.dataset.role === selectedRole) {
                item.style.backgroundColor = '#f8f9fa';
                item.style.border = '2px solid #007bff';
                item.style.borderRadius = '4px';
            } else {
                item.style.backgroundColor = '';
                item.style.border = '';
                item.style.borderRadius = '';
            }
        });
    }
    
    roleSelect.addEventListener('change', updateRoleInfo);
    updateRoleInfo();
});
</script>
@endpush

@extends('layouts.template')

@section('title', 'Rapports - CRFM')

@section('page-title', 'Rapports et Analyses')
@section('page-description', 'Génération de rapports détaillés')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Rapports</a></li>
    </ul>
@endsection

@section('content')
<div class="row">
    <!-- Résumé des données -->
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-blue text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($summary['adherents']['total']) }}</h4>
                        <h6 class="text-white m-b-0">Total Adhérents</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-users f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-green text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($summary['cotisations']['total_annee'], 0) }}€</h4>
                        <h6 class="text-white m-b-0">Cotisations Année</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-credit-card f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-yellow text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($summary['pensions']['dossiers_en_cours']) }}</h4>
                        <h6 class="text-white m-b-0">Dossiers en cours</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-briefcase f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-purple text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($summary['pensions']['montant_liquide_annee'], 0) }}€</h4>
                        <h6 class="text-white m-b-0">Pensions liquidées</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-dollar-sign f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Types de rapports disponibles -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-file-text text-c-blue me-2"></i>Rapports Adhérents</h5>
            </div>
            <div class="card-block">
                <p class="text-muted">Générez des rapports détaillés sur les adhérents</p>
                <div class="d-grid gap-2">
                    <a href="{{ route('reports.adherents.form') }}" class="btn btn-primary">
                        <i class="feather icon-users me-2"></i>Rapport Adhérents
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-credit-card text-c-green me-2"></i>Rapports Cotisations</h5>
            </div>
            <div class="card-block">
                <p class="text-muted">Analysez les cotisations et paiements</p>
                <div class="d-grid gap-2">
                    <a href="{{ route('reports.cotisations.form') }}" class="btn btn-success">
                        <i class="feather icon-credit-card me-2"></i>Rapport Cotisations
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-briefcase text-c-yellow me-2"></i>Rapports Pensions</h5>
            </div>
            <div class="card-block">
                <p class="text-muted">Suivez les dossiers de pension et liquidations</p>
                <div class="d-grid gap-2">
                    <a href="{{ route('reports.pensions.form') }}" class="btn btn-warning">
                        <i class="feather icon-briefcase me-2"></i>Rapport Pensions
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-bar-chart text-c-purple me-2"></i>Rapports Financiers</h5>
            </div>
            <div class="card-block">
                <p class="text-muted">Synthèse financière et indicateurs</p>
                <div class="d-grid gap-2">
                    <a href="{{ route('reports.financial.form') }}" class="btn btn-info">
                        <i class="feather icon-bar-chart me-2"></i>Rapport Financier
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Actions rapides -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-download text-c-blue me-2"></i>Actions rapides</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-outline-primary btn-block" onclick="exportRapportMensuel()">
                            <i class="feather icon-calendar me-2"></i>Rapport mensuel
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-success btn-block" onclick="exportRapportAnnuel()">
                            <i class="feather icon-calendar me-2"></i>Rapport annuel
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-info btn-block" onclick="exportToutesStatistiques()">
                            <i class="feather icon-download me-2"></i>Export complet
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary btn-block" onclick="imprimerPage()">
                            <i class="feather icon-printer me-2"></i>Imprimer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function exportRapportMensuel() {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    
    window.location.href = `/api/reports/export?type=monthly&year=${year}&month=${month}`;
}

function exportRapportAnnuel() {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    
    window.location.href = `/api/reports/export?type=yearly&year=${year}`;
}

function exportToutesStatistiques() {
    window.location.href = '/api/reports/export?type=complete';
}

function imprimerPage() {
    window.print();
}
</script>
@endpush

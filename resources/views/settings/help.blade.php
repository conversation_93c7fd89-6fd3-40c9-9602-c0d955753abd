@extends('layouts.template')

@section('title', 'Aide - Paramètres CRFM')

@section('page-title', 'Guide des Paramètres')
@section('page-description', 'Documentation et aide pour la configuration du système')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('parametres.index') }}">Paramètres</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Aide</a></li>
    </ul>
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-help-circle text-c-blue me-2"></i>Guide de Configuration</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-3">
                        <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist">
                            <button class="nav-link active" id="v-pills-general-tab" data-bs-toggle="pill" data-bs-target="#v-pills-general" type="button" role="tab">
                                <i class="feather icon-settings me-2"></i>Paramètres Généraux
                            </button>
                            <button class="nav-link" id="v-pills-profile-tab" data-bs-toggle="pill" data-bs-target="#v-pills-profile" type="button" role="tab">
                                <i class="feather icon-user me-2"></i>Gestion du Profil
                            </button>
                            <button class="nav-link" id="v-pills-commands-tab" data-bs-toggle="pill" data-bs-target="#v-pills-commands" type="button" role="tab">
                                <i class="feather icon-terminal me-2"></i>Commandes CLI
                            </button>
                            <button class="nav-link" id="v-pills-troubleshoot-tab" data-bs-toggle="pill" data-bs-target="#v-pills-troubleshoot" type="button" role="tab">
                                <i class="feather icon-tool me-2"></i>Dépannage
                            </button>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="tab-content" id="v-pills-tabContent">
                            <!-- Paramètres Généraux -->
                            <div class="tab-pane fade show active" id="v-pills-general" role="tabpanel">
                                <h6 class="mb-3">🔧 Configuration des Paramètres Système</h6>
                                
                                <div class="alert alert-info">
                                    <h6><i class="feather icon-info me-2"></i>Navigation</h6>
                                    <ul class="mb-0">
                                        <li><strong>Tableau de bord</strong> : Vue d'ensemble des paramètres</li>
                                        <li><strong>Configuration système</strong> : Interface complète avec onglets</li>
                                        <li><strong>Paramètres avancés</strong> : Modification individuelle</li>
                                    </ul>
                                </div>

                                <h6 class="mt-4">📋 Groupes de Paramètres</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card border-primary">
                                            <div class="card-body">
                                                <h6 class="card-title text-primary">Général</h6>
                                                <p class="card-text small">Nom de l'application, organisation, informations de contact</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-success">
                                            <div class="card-body">
                                                <h6 class="card-title text-success">Système</h6>
                                                <p class="card-text small">Fuseau horaire, format de date, devise, pagination</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Gestion du Profil -->
                            <div class="tab-pane fade" id="v-pills-profile" role="tabpanel">
                                <h6 class="mb-3">👤 Gestion du Profil Utilisateur</h6>
                                
                                <div class="alert alert-success">
                                    <h6><i class="feather icon-check me-2"></i>Fonctionnalités Disponibles</h6>
                                    <ul class="mb-0">
                                        <li>✅ Modification des informations personnelles</li>
                                        <li>✅ Upload et gestion d'avatar</li>
                                        <li>✅ Changement de mot de passe sécurisé</li>
                                        <li>✅ Préférences utilisateur (notifications, langue)</li>
                                        <li>✅ Suivi de la dernière connexion</li>
                                    </ul>
                                </div>

                                <div class="alert alert-warning">
                                    <h6><i class="feather icon-shield me-2"></i>Sécurité</h6>
                                    <p class="mb-0">Le mot de passe actuel est requis pour modifier le mot de passe. Les avatars sont automatiquement redimensionnés et optimisés.</p>
                                </div>
                            </div>

                            <!-- Commandes CLI -->
                            <div class="tab-pane fade" id="v-pills-commands" role="tabpanel">
                                <h6 class="mb-3">💻 Commandes en Ligne de Commande</h6>
                                
                                <div class="card">
                                    <div class="card-body">
                                        <h6>📋 Lister tous les paramètres</h6>
                                        <code class="d-block bg-dark text-light p-2 rounded">php artisan settings:manage list</code>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-body">
                                        <h6>🔍 Obtenir un paramètre spécifique</h6>
                                        <code class="d-block bg-dark text-light p-2 rounded">php artisan settings:manage get app_name</code>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-body">
                                        <h6>✏️ Définir un paramètre</h6>
                                        <code class="d-block bg-dark text-light p-2 rounded">php artisan settings:manage set key "valeur" --type=string --group=general</code>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-body">
                                        <h6>🗑️ Supprimer un paramètre</h6>
                                        <code class="d-block bg-dark text-light p-2 rounded">php artisan settings:manage delete key</code>
                                    </div>
                                </div>
                            </div>

                            <!-- Dépannage -->
                            <div class="tab-pane fade" id="v-pills-troubleshoot" role="tabpanel">
                                <h6 class="mb-3">🔧 Dépannage et Maintenance</h6>
                                
                                <div class="alert alert-info">
                                    <h6><i class="feather icon-info me-2"></i>Actions de Maintenance</h6>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <form method="POST" action="{{ route('settings.clear-cache') }}">
                                                @csrf
                                                <button type="submit" class="btn btn-warning btn-block" 
                                                        onclick="return confirm('Vider le cache système ?')">
                                                    <i class="feather icon-trash-2 me-2"></i>Vider le cache
                                                </button>
                                            </form>
                                        </div>
                                        <div class="col-md-6">
                                            <button type="button" class="btn btn-info btn-block" data-bs-toggle="modal" data-bs-target="#testEmailModal">
                                                <i class="feather icon-mail me-2"></i>Tester la configuration email
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-3">
                                    <div class="card-body">
                                        <h6>🚨 Problèmes Courants</h6>
                                        <ul>
                                            <li><strong>Cache non mis à jour</strong> : Utilisez le bouton "Vider cache"</li>
                                            <li><strong>Emails non envoyés</strong> : Vérifiez la configuration SMTP</li>
                                            <li><strong>Avatar non affiché</strong> : Vérifiez les permissions du dossier storage</li>
                                            <li><strong>Paramètres non sauvegardés</strong> : Vérifiez les permissions de la base de données</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de test email -->
<div class="modal fade" id="testEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test de configuration email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('settings.test-email') }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="test_email" class="form-label">Adresse email de test</label>
                        <input type="email" class="form-control" id="test_email" name="test_email" 
                               value="{{ auth()->user()->email }}" required>
                        <small class="form-text text-muted">Un email de test sera envoyé à cette adresse.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Envoyer test</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function resetForm() {
    if (confirm('Réinitialiser tous les champs ?')) {
        document.querySelector('form').reset();
    }
}

// Validation du formulaire
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});
</script>
@endpush

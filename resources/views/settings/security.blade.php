@extends('layouts.template')

@section('title', 'Sécurité - CRFM')

@section('page-title', 'Paramètres de Sécurité')
@section('page-description', 'Configuration de la sécurité et des politiques d\'accès')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('parametres.index') }}">Paramètres</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Sécurité</a></li>
    </ul>
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-shield text-c-red me-2"></i>Configuration de la Sécurité</h5>
            </div>
            <div class="card-block">
                <div class="alert alert-info">
                    <i class="feather icon-info me-2"></i>
                    <strong>Fonctionnalité en développement</strong><br>
                    Cette section sera bientôt disponible pour configurer les paramètres de sécurité avancés.
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-warning">
                            <div class="card-body">
                                <h6 class="card-title text-warning">
                                    <i class="feather icon-lock me-2"></i>Politiques de Mot de Passe
                                </h6>
                                <p class="card-text">Configuration des règles de complexité des mots de passe.</p>
                                <button class="btn btn-outline-warning btn-sm" disabled>
                                    <i class="feather icon-settings me-1"></i>Configurer
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-danger">
                            <div class="card-body">
                                <h6 class="card-title text-danger">
                                    <i class="feather icon-user-x me-2"></i>Gestion des Sessions
                                </h6>
                                <p class="card-text">Contrôle des sessions utilisateur et déconnexions automatiques.</p>
                                <button class="btn btn-outline-danger btn-sm" disabled>
                                    <i class="feather icon-settings me-1"></i>Configurer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-body">
                                <h6 class="card-title text-info">
                                    <i class="feather icon-eye me-2"></i>Audit et Logs
                                </h6>
                                <p class="card-text">Surveillance des activités et journalisation des événements.</p>
                                <button class="btn btn-outline-info btn-sm" disabled>
                                    <i class="feather icon-settings me-1"></i>Configurer
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-body">
                                <h6 class="card-title text-success">
                                    <i class="feather icon-key me-2"></i>Authentification 2FA
                                </h6>
                                <p class="card-text">Configuration de l'authentification à deux facteurs.</p>
                                <button class="btn btn-outline-success btn-sm" disabled>
                                    <i class="feather icon-settings me-1"></i>Configurer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="{{ route('parametres.index') }}" class="btn btn-secondary">
                        <i class="feather icon-arrow-left me-2"></i>Retour aux paramètres
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@extends('layouts.template')

@section('title', 'Paramètres Système - CRFM')

@section('page-title', 'Paramètres Système')
@section('page-description', 'Configuration générale du système')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Paramètres</a></li>
    </ul>
@endsection

@section('content')
<div class="row">
    <!-- Navigation des onglets -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                            <i class="feather icon-settings me-2"></i>Général
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="organization-tab" data-bs-toggle="tab" data-bs-target="#organization" type="button" role="tab">
                            <i class="feather icon-building me-2"></i>Organisation
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab">
                            <i class="feather icon-image me-2"></i>Apparence
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                            <i class="feather icon-server me-2"></i>Système
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<form method="POST" action="{{ route('settings.update') }}" enctype="multipart/form-data" class="needs-validation" novalidate>
    @csrf
    @method('PUT')
    
    <div class="tab-content" id="settingsTabContent">
        <!-- Onglet Général -->
        <div class="tab-pane fade show active" id="general" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-settings text-c-blue me-2"></i>Paramètres généraux</h5>
                        </div>
                        <div class="card-block">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="required">Nom de l'application</label>
                                        <input type="text" name="app_name" class="form-control {{ $errors->has('app_name') ? 'is-invalid' : '' }}" 
                                               value="{{ old('app_name', $settings['app_name']) }}" required>
                                        @if($errors->has('app_name'))
                                            <div class="invalid-feedback">{{ $errors->first('app_name') }}</div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="required">Fuseau horaire</label>
                                        <select name="timezone" class="form-control {{ $errors->has('timezone') ? 'is-invalid' : '' }}" required>
                                            <option value="Africa/Bamako" {{ $settings['timezone'] == 'Africa/Bamako' ? 'selected' : '' }}>Africa/Bamako</option>
                                            <option value="UTC" {{ $settings['timezone'] == 'UTC' ? 'selected' : '' }}>UTC</option>
                                            <option value="Europe/Paris" {{ $settings['timezone'] == 'Europe/Paris' ? 'selected' : '' }}>Europe/Paris</option>
                                        </select>
                                        @if($errors->has('timezone'))
                                            <div class="invalid-feedback">{{ $errors->first('timezone') }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Description de l'application</label>
                                <textarea name="app_description" class="form-control {{ $errors->has('app_description') ? 'is-invalid' : '' }}" 
                                          rows="3">{{ old('app_description', $settings['app_description']) }}</textarea>
                                @if($errors->has('app_description'))
                                    <div class="invalid-feedback">{{ $errors->first('app_description') }}</div>
                                @endif
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="required">Format de date</label>
                                        <select name="date_format" class="form-control {{ $errors->has('date_format') ? 'is-invalid' : '' }}" required>
                                            <option value="d/m/Y" {{ $settings['date_format'] == 'd/m/Y' ? 'selected' : '' }}>DD/MM/YYYY</option>
                                            <option value="m/d/Y" {{ $settings['date_format'] == 'm/d/Y' ? 'selected' : '' }}>MM/DD/YYYY</option>
                                            <option value="Y-m-d" {{ $settings['date_format'] == 'Y-m-d' ? 'selected' : '' }}>YYYY-MM-DD</option>
                                        </select>
                                        @if($errors->has('date_format'))
                                            <div class="invalid-feedback">{{ $errors->first('date_format') }}</div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="required">Devise</label>
                                        <select name="currency" class="form-control {{ $errors->has('currency') ? 'is-invalid' : '' }}" required>
                                            <option value="FCFA" {{ $settings['currency'] == 'FCFA' ? 'selected' : '' }}>FCFA</option>
                                            <option value="EUR" {{ $settings['currency'] == 'EUR' ? 'selected' : '' }}>EUR</option>
                                            <option value="USD" {{ $settings['currency'] == 'USD' ? 'selected' : '' }}>USD</option>
                                        </select>
                                        @if($errors->has('currency'))
                                            <div class="invalid-feedback">{{ $errors->first('currency') }}</div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="required">Langue</label>
                                        <select name="language" class="form-control {{ $errors->has('language') ? 'is-invalid' : '' }}" required>
                                            <option value="fr" {{ $settings['language'] == 'fr' ? 'selected' : '' }}>Français</option>
                                            <option value="en" {{ $settings['language'] == 'en' ? 'selected' : '' }}>English</option>
                                        </select>
                                        @if($errors->has('language'))
                                            <div class="invalid-feedback">{{ $errors->first('language') }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-sliders text-c-green me-2"></i>Préférences</h5>
                        </div>
                        <div class="card-block">
                            <div class="form-group">
                                <label class="required">Éléments par page</label>
                                <select name="items_per_page" class="form-control {{ $errors->has('items_per_page') ? 'is-invalid' : '' }}" required>
                                    <option value="10" {{ $settings['items_per_page'] == 10 ? 'selected' : '' }}>10</option>
                                    <option value="15" {{ $settings['items_per_page'] == 15 ? 'selected' : '' }}>15</option>
                                    <option value="25" {{ $settings['items_per_page'] == 25 ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ $settings['items_per_page'] == 50 ? 'selected' : '' }}>50</option>
                                </select>
                                @if($errors->has('items_per_page'))
                                    <div class="invalid-feedback">{{ $errors->first('items_per_page') }}</div>
                                @endif
                            </div>
                            
                            <div class="form-group">
                                <label class="required">Timeout de session (minutes)</label>
                                <input type="number" name="session_timeout" class="form-control {{ $errors->has('session_timeout') ? 'is-invalid' : '' }}"
                                       value="{{ old('session_timeout', $settings['session_timeout']) }}" min="15" max="1440" required>
                                @if($errors->has('session_timeout'))
                                    <div class="invalid-feedback">{{ $errors->first('session_timeout') }}</div>
                                @endif
                            </div>

                            <div class="form-group">
                                <label class="required">Fréquence de sauvegarde</label>
                                <select name="backup_frequency" class="form-control {{ $errors->has('backup_frequency') ? 'is-invalid' : '' }}" required>
                                    <option value="daily" {{ old('backup_frequency', $settings['backup_frequency']) == 'daily' ? 'selected' : '' }}>Quotidienne</option>
                                    <option value="weekly" {{ old('backup_frequency', $settings['backup_frequency']) == 'weekly' ? 'selected' : '' }}>Hebdomadaire</option>
                                    <option value="monthly" {{ old('backup_frequency', $settings['backup_frequency']) == 'monthly' ? 'selected' : '' }}>Mensuelle</option>
                                </select>
                                @if($errors->has('backup_frequency'))
                                    <div class="invalid-feedback">{{ $errors->first('backup_frequency') }}</div>
                                @endif
                            </div>

                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="registration_enabled" class="form-check-input" id="registration_enabled" 
                                           value="1" {{ $settings['registration_enabled'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="registration_enabled">
                                        Permettre l'inscription
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="maintenance_mode" class="form-check-input" id="maintenance_mode" 
                                           value="1" {{ $settings['maintenance_mode'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="maintenance_mode">
                                        Mode maintenance
                                    </label>
                                </div>
                                <small class="form-text text-muted">Seuls les administrateurs pourront accéder au système</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Organisation -->
        <div class="tab-pane fade" id="organization" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-building text-c-blue me-2"></i>Informations de l'organisation</h5>
                        </div>
                        <div class="card-block">
                            <div class="form-group">
                                <label class="required">Nom de l'organisation</label>
                                <input type="text" name="organization_name" class="form-control {{ $errors->has('organization_name') ? 'is-invalid' : '' }}" 
                                       value="{{ old('organization_name', $settings['organization_name']) }}" required>
                                @if($errors->has('organization_name'))
                                    <div class="invalid-feedback">{{ $errors->first('organization_name') }}</div>
                                @endif
                            </div>
                            
                            <div class="form-group">
                                <label>Adresse</label>
                                <textarea name="organization_address" class="form-control {{ $errors->has('organization_address') ? 'is-invalid' : '' }}" 
                                          rows="3">{{ old('organization_address', $settings['organization_address']) }}</textarea>
                                @if($errors->has('organization_address'))
                                    <div class="invalid-feedback">{{ $errors->first('organization_address') }}</div>
                                @endif
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Téléphone</label>
                                        <input type="text" name="organization_phone" class="form-control {{ $errors->has('organization_phone') ? 'is-invalid' : '' }}" 
                                               value="{{ old('organization_phone', $settings['organization_phone']) }}">
                                        @if($errors->has('organization_phone'))
                                            <div class="invalid-feedback">{{ $errors->first('organization_phone') }}</div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Email</label>
                                        <input type="email" name="organization_email" class="form-control {{ $errors->has('organization_email') ? 'is-invalid' : '' }}" 
                                               value="{{ old('organization_email', $settings['organization_email']) }}">
                                        @if($errors->has('organization_email'))
                                            <div class="invalid-feedback">{{ $errors->first('organization_email') }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Site web</label>
                                <input type="url" name="organization_website" class="form-control {{ $errors->has('organization_website') ? 'is-invalid' : '' }}" 
                                       value="{{ old('organization_website', $settings['organization_website']) }}">
                                @if($errors->has('organization_website'))
                                    <div class="invalid-feedback">{{ $errors->first('organization_website') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Boutons d'action -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-block text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="feather icon-save me-2"></i>Enregistrer les paramètres
                    </button>
                    <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="resetForm()">
                        <i class="feather icon-refresh-cw me-2"></i>Réinitialiser
                    </button>

                    <!-- Actions supplémentaires -->
                    <div class="mt-3">
                        <a href="{{ route('settings.advanced') }}" class="btn btn-info me-2">
                            <i class="feather icon-tool me-1"></i>Paramètres avancés
                        </a>
                        <a href="{{ route('settings.export') }}" class="btn btn-success me-2">
                            <i class="feather icon-download me-1"></i>Exporter
                        </a>
                        <button type="button" class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#importModal">
                            <i class="feather icon-upload me-1"></i>Importer
                        </button>
                        <form method="POST" action="{{ route('settings.clear-cache') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger"
                                    onclick="return confirm('Êtes-vous sûr de vouloir vider le cache ?')">
                                <i class="feather icon-trash-2 me-1"></i>Vider cache
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<style>
.required::after {
    content: " *";
    color: red;
}
.form-group {
    margin-bottom: 1rem;
}
.card-block {
    padding: 1.5rem;
}
.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
}
.nav-tabs .nav-link.active {
    border-bottom-color: #007bff;
    background-color: transparent;
}
.tab-content {
    padding-top: 1rem;
}
</style>
@endpush

<!-- Modal d'importation -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Importer des paramètres</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('settings.import') }}" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="settings_file" class="form-label">Fichier de paramètres (JSON)</label>
                        <input type="file" class="form-control" id="settings_file" name="settings_file" accept=".json" required>
                        <small class="form-text text-muted">Sélectionnez un fichier JSON contenant les paramètres à importer.</small>
                    </div>
                    <div class="alert alert-warning">
                        <i class="feather icon-alert-triangle me-2"></i>
                        <strong>Attention :</strong> Cette action remplacera les paramètres existants.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">Importer</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function resetForm() {
    if (confirm('Réinitialiser tous les champs ?')) {
        document.querySelector('form').reset();
    }
}

// Validation du formulaire
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});
</script>
@endpush

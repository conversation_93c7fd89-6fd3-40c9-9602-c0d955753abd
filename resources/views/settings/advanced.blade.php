@extends('layouts.template')

@section('title', 'Paramètres Avancés - CRFM')

@section('page-title', 'Paramètres Avancés')
@section('page-description', 'Configuration avancée du système')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('parametres.index') }}">Paramètres</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Avancés</a></li>
    </ul>
@endsection

@section('content')
<div class="row">
    <!-- Actions rapides -->
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-zap text-c-orange me-2"></i>Actions rapides</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-3">
                        <form method="POST" action="{{ route('settings.clear-cache') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-warning btn-block" 
                                    onclick="return confirm('Êtes-vous sûr de vouloir vider le cache ?')">
                                <i class="feather icon-trash-2 me-2"></i>Vider le cache
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('settings.export') }}" class="btn btn-info btn-block">
                            <i class="feather icon-download me-2"></i>Exporter paramètres
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-success btn-block" data-bs-toggle="modal" data-bs-target="#importModal">
                            <i class="feather icon-upload me-2"></i>Importer paramètres
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-primary btn-block" data-bs-toggle="modal" data-bs-target="#testEmailModal">
                            <i class="feather icon-mail me-2"></i>Test email
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Paramètres par groupe -->
    @if(isset($settingsFromDb))
        @foreach($settingsFromDb as $groupName => $groupSettings)
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5>
                        @switch($groupName)
                            @case('general')
                                <i class="feather icon-settings text-c-blue me-2"></i>Général
                                @break
                            @case('appearance')
                                <i class="feather icon-eye text-c-purple me-2"></i>Apparence
                                @break
                            @case('system')
                                <i class="feather icon-server text-c-green me-2"></i>Système
                                @break
                            @case('notifications')
                                <i class="feather icon-bell text-c-orange me-2"></i>Notifications
                                @break
                            @case('security')
                                <i class="feather icon-shield text-c-red me-2"></i>Sécurité
                                @break
                            @default
                                <i class="feather icon-folder text-c-gray me-2"></i>{{ ucfirst($groupName) }}
                        @endswitch
                    </h5>
                </div>
                <div class="card-block">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Paramètre</th>
                                    <th>Valeur</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($groupSettings as $setting)
                                <tr>
                                    <td>
                                        <strong>{{ $setting->label }}</strong>
                                        @if($setting->description)
                                            <br><small class="text-muted">{{ $setting->description }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($setting->type === 'boolean')
                                            <span class="badge bg-{{ $setting->value === 'true' ? 'success' : 'secondary' }}">
                                                {{ $setting->value === 'true' ? 'Activé' : 'Désactivé' }}
                                            </span>
                                        @else
                                            <code>{{ Str::limit($setting->value, 30) }}</code>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ $setting->type }}</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="editSetting('{{ $setting->key }}', '{{ $setting->value }}', '{{ $setting->type }}', '{{ $setting->label }}')">
                                            <i class="feather icon-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    @endif
</div>

<!-- Modal d'importation -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Importer des paramètres</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('settings.import') }}" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="settings_file" class="form-label">Fichier de paramètres (JSON)</label>
                        <input type="file" class="form-control" id="settings_file" name="settings_file" accept=".json" required>
                        <small class="form-text text-muted">Sélectionnez un fichier JSON contenant les paramètres à importer.</small>
                    </div>
                    <div class="alert alert-warning">
                        <i class="feather icon-alert-triangle me-2"></i>
                        <strong>Attention :</strong> Cette action remplacera les paramètres existants.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">Importer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de test email -->
<div class="modal fade" id="testEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test de configuration email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('settings.test-email') }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="test_email" class="form-label">Adresse email de test</label>
                        <input type="email" class="form-control" id="test_email" name="test_email" 
                               value="{{ auth()->user()->email }}" required>
                        <small class="form-text text-muted">Un email de test sera envoyé à cette adresse.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Envoyer test</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal d'édition de paramètre -->
<div class="modal fade" id="editSettingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifier le paramètre</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editSettingForm" method="POST" action="{{ route('settings.update-single') }}">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <input type="hidden" id="edit_key" name="key">
                    <div class="mb-3">
                        <label for="edit_label" class="form-label">Nom du paramètre</label>
                        <input type="text" class="form-control" id="edit_label" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="edit_value" class="form-label">Valeur</label>
                        <input type="text" class="form-control" id="edit_value" name="value" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_type" class="form-label">Type</label>
                        <select class="form-control" id="edit_type" name="type" required>
                            <option value="string">Texte</option>
                            <option value="boolean">Booléen</option>
                            <option value="integer">Entier</option>
                            <option value="json">JSON</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Sauvegarder</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function editSetting(key, value, type, label) {
    document.getElementById('edit_key').value = key;
    document.getElementById('edit_label').value = label;
    document.getElementById('edit_value').value = value;
    document.getElementById('edit_type').value = type;
    
    // Adapter le champ de valeur selon le type
    const valueInput = document.getElementById('edit_value');
    if (type === 'boolean') {
        valueInput.type = 'checkbox';
        valueInput.checked = value === 'true';
    } else {
        valueInput.type = 'text';
    }
    
    new bootstrap.Modal(document.getElementById('editSettingModal')).show();
}

// Gérer le changement de type
document.getElementById('edit_type').addEventListener('change', function() {
    const valueInput = document.getElementById('edit_value');
    const currentValue = valueInput.value;
    
    if (this.value === 'boolean') {
        valueInput.type = 'checkbox';
        valueInput.checked = currentValue === 'true' || currentValue === '1';
    } else {
        valueInput.type = 'text';
        if (valueInput.type === 'checkbox') {
            valueInput.value = valueInput.checked ? 'true' : 'false';
        }
    }
});

// Gérer la soumission du formulaire d'édition
document.getElementById('editSettingForm').addEventListener('submit', function(e) {
    const typeSelect = document.getElementById('edit_type');
    const valueInput = document.getElementById('edit_value');
    
    if (typeSelect.value === 'boolean') {
        valueInput.value = valueInput.checked ? 'true' : 'false';
    }
});
</script>
@endpush

@extends('layouts.template')

@section('title', 'Paramètres - CRFM')

@section('page-title', 'Centre de Paramètres')
@section('page-description', 'Configuration et administration du système')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Paramètres</a></li>
    </ul>
@endsection

@section('content')
<div class="row">
    <!-- Configuration Système -->
    <div class="col-xl-4 col-md-6">
        <div class="card settings-card">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-c-blue f-w-600">Configuration</h4>
                        <h6 class="text-muted m-b-0">Paramètres système</h6>
                        <p class="text-muted mt-2">G<PERSON>rer les paramètres généraux, l'apparence et les préférences du système.</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-settings f-28 text-c-blue"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ route('settings.index') }}" class="btn btn-primary btn-sm me-2">
                        <i class="feather icon-settings me-1"></i>Configuration
                    </a>
                    <a href="{{ route('settings.advanced') }}" class="btn btn-outline-primary btn-sm">
                        <i class="feather icon-tool me-1"></i>Avancé
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Gestion des Utilisateurs -->
    <div class="col-xl-4 col-md-6">
        <div class="card settings-card">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-c-green f-w-600">Utilisateurs</h4>
                        <h6 class="text-muted m-b-0">{{ App\Models\User::count() }} utilisateurs</h6>
                        <p class="text-muted mt-2">Gérer les comptes utilisateurs, les rôles et les permissions.</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-users f-28 text-c-green"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ route('users.index') }}" class="btn btn-success btn-sm">
                        <i class="feather icon-arrow-right me-1"></i>Gérer
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Rôles et Permissions -->
    <div class="col-xl-4 col-md-6">
        <div class="card settings-card">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-c-orange f-w-600">Rôles</h4>
                        <h6 class="text-muted m-b-0">Permissions utilisateurs</h6>
                        <p class="text-muted mt-2">Gérer les rôles et les permissions des utilisateurs du système.</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-shield f-28 text-c-orange"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ route('roles.index') }}" class="btn btn-warning btn-sm">
                        <i class="feather icon-arrow-right me-1"></i>Gérer
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications -->
    <div class="col-xl-4 col-md-6">
        <div class="card settings-card">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-c-purple f-w-600">Notifications</h4>
                        <h6 class="text-muted m-b-0">Email & SMS</h6>
                        <p class="text-muted mt-2">Configurer les notifications automatiques et les alertes système.</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-bell f-28 text-c-purple"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ route('notifications.index') }}" class="btn btn-info btn-sm">
                        <i class="feather icon-arrow-right me-1"></i>Configurer
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Sécurité -->
    <div class="col-xl-4 col-md-6">
        <div class="card settings-card">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-c-red f-w-600">Sécurité</h4>
                        <h6 class="text-muted m-b-0">Politique de sécurité</h6>
                        <p class="text-muted mt-2">Configurer les paramètres de sécurité et les politiques d'accès.</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-lock f-28 text-c-red"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ route('security.index') }}" class="btn btn-danger btn-sm">
                        <i class="feather icon-arrow-right me-1"></i>Configurer
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Outils Système -->
    <div class="col-xl-4 col-md-6">
        <div class="card settings-card">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-c-blue f-w-600">Outils</h4>
                        <h6 class="text-muted m-b-0">Maintenance système</h6>
                        <p class="text-muted mt-2">Outils de maintenance, cache, logs et diagnostics système.</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-tool f-28 text-c-blue"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <form method="POST" action="{{ route('settings.clear-cache') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-warning btn-sm me-2"
                                onclick="return confirm('Êtes-vous sûr de vouloir vider le cache ?')">
                            <i class="feather icon-trash-2 me-1"></i>Vider cache
                        </button>
                    </form>
                    <button class="btn btn-outline-info btn-sm" onclick="showSystemInfo()">
                        <i class="feather icon-info me-1"></i>Infos système
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Documentation et Aide -->
    <div class="col-xl-4 col-md-6">
        <div class="card settings-card">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-c-green f-w-600">Documentation</h4>
                        <h6 class="text-muted m-b-0">Aide et support</h6>
                        <p class="text-muted mt-2">Guide d'utilisation, commandes CLI et dépannage.</p>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-help-circle f-28 text-c-green"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ route('settings.help') }}" class="btn btn-success btn-sm">
                        <i class="feather icon-book me-1"></i>Guide d'aide
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-activity text-c-blue me-2"></i>Activité récente</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-c-blue">{{ App\Models\User::count() }}</h4>
                            <p class="text-muted">Utilisateurs totaux</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-c-green">{{ App\Models\User::where('is_active', true)->count() }}</h4>
                            <p class="text-muted">Utilisateurs actifs</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-c-orange">{{ App\Models\User::where('role', 'admin')->count() }}</h4>
                            <p class="text-muted">Administrateurs</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-c-purple">{{ config('app.env') === 'production' ? 'Production' : 'Développement' }}</h4>
                            <p class="text-muted">Environnement</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-zap text-c-orange me-2"></i>Actions rapides</h5>
            </div>
            <div class="card-block">
                <div class="btn-group" role="group">
                    <a href="{{ route('users.create') }}" class="btn btn-primary">
                        <i class="feather icon-user-plus me-2"></i>Nouvel utilisateur
                    </a>
                    <button type="button" class="btn btn-info" onclick="clearCache()">
                        <i class="feather icon-refresh-cw me-2"></i>Vider le cache
                    </button>
                    <a href="{{ route('settings.export') }}" class="btn btn-secondary">
                        <i class="feather icon-download me-2"></i>Exporter paramètres
                    </a>
                    <button type="button" class="btn btn-warning" onclick="showSystemInfo()">
                        <i class="feather icon-info me-2"></i>Infos système
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'informations système -->
<div class="modal fade" id="systemInfoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Informations système</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Application</h6>
                        <ul class="list-unstyled">
                            <li><strong>Nom :</strong> {{ config('app.name') }}</li>
                            <li><strong>Version Laravel :</strong> {{ app()->version() }}</li>
                            <li><strong>Environnement :</strong> {{ config('app.env') }}</li>
                            <li><strong>Debug :</strong> {{ config('app.debug') ? 'Activé' : 'Désactivé' }}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Serveur</h6>
                        <ul class="list-unstyled">
                            <li><strong>PHP :</strong> {{ PHP_VERSION }}</li>
                            <li><strong>Serveur :</strong> {{ $_SERVER['SERVER_SOFTWARE'] ?? 'N/A' }}</li>
                            <li><strong>OS :</strong> {{ PHP_OS }}</li>
                            <li><strong>Timezone :</strong> {{ config('app.timezone') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.settings-card {
    transition: transform 0.2s ease-in-out;
    border: 1px solid #e9ecef;
}
.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.card-block {
    padding: 1.5rem;
}
.f-28 {
    font-size: 28px;
}
</style>
@endpush

@push('scripts')
<script>
function clearCache() {
    if (confirm('Vider le cache du système ?')) {
        fetch('{{ route("settings.clear-cache") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            CRFM.showToast('Cache vidé avec succès', 'success');
        })
        .catch(error => {
            CRFM.showToast('Erreur lors du vidage du cache', 'error');
        });
    }
}

function showSystemInfo() {
    const modal = new bootstrap.Modal(document.getElementById('systemInfoModal'));
    modal.show();
}

function showComingSoon() {
    CRFM.showToast('Fonctionnalité en cours de développement', 'info');
}
</script>
@endpush

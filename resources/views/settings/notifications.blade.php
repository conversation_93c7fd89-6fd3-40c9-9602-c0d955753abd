@extends('layouts.template')

@section('title', 'Notifications - CRFM')

@section('page-title', 'Gestion des Notifications')
@section('page-description', 'Configuration des notifications système et utilisateur')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('parametres.index') }}">Paramètres</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Notifications</a></li>
    </ul>
@endsection

@section('content')
<div class="row">
    <!-- Configuration des notifications -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-bell text-c-orange me-2"></i>Configuration des Notifications</h5>
            </div>
            <div class="card-block">
                @if (session('success'))
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="feather icon-check-circle me-2"></i>{{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <form method="POST" action="{{ route('notifications.update') }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">📧 Notifications Email</h6>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_enabled" name="email_enabled" checked>
                                <label class="form-check-label" for="email_enabled">
                                    Activer les notifications email
                                </label>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_new_user" name="email_new_user" checked>
                                <label class="form-check-label" for="email_new_user">
                                    Nouvel utilisateur inscrit
                                </label>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_password_reset" name="email_password_reset" checked>
                                <label class="form-check-label" for="email_password_reset">
                                    Réinitialisation de mot de passe
                                </label>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_system_alerts" name="email_system_alerts" checked>
                                <label class="form-check-label" for="email_system_alerts">
                                    Alertes système
                                </label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="mb-3">📱 Notifications SMS</h6>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="sms_enabled" name="sms_enabled">
                                <label class="form-check-label" for="sms_enabled">
                                    Activer les notifications SMS
                                </label>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="sms_urgent_alerts" name="sms_urgent_alerts">
                                <label class="form-check-label" for="sms_urgent_alerts">
                                    Alertes urgentes uniquement
                                </label>
                            </div>

                            <div class="mb-3">
                                <label for="sms_provider" class="form-label">Fournisseur SMS</label>
                                <select class="form-control" id="sms_provider" name="sms_provider">
                                    <option value="">Sélectionner un fournisseur</option>
                                    <option value="orange">Orange Mali</option>
                                    <option value="malitel">Malitel</option>
                                    <option value="telecel">Telecel Mali</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3">⚙️ Paramètres Avancés</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notification_frequency" class="form-label">Fréquence des notifications</label>
                                <select class="form-control" id="notification_frequency" name="notification_frequency">
                                    <option value="immediate">Immédiate</option>
                                    <option value="hourly">Toutes les heures</option>
                                    <option value="daily" selected>Quotidienne</option>
                                    <option value="weekly">Hebdomadaire</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quiet_hours_start" class="form-label">Heures silencieuses (début)</label>
                                <input type="time" class="form-control" id="quiet_hours_start" name="quiet_hours_start" value="22:00">
                            </div>
                        </div>
                    </div>

                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="feather icon-save me-2"></i>Enregistrer les paramètres
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="resetForm()">
                            <i class="feather icon-refresh-cw me-2"></i>Réinitialiser
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modèles de notifications -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-file-text text-c-blue me-2"></i>Modèles de Notifications</h5>
            </div>
            <div class="card-block">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Modèle</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <strong>Bienvenue</strong><br>
                                    <small class="text-muted">Nouvel utilisateur</small>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editTemplate('welcome')">
                                        <i class="feather icon-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="previewTemplate('welcome')">
                                        <i class="feather icon-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <strong>Mot de passe</strong><br>
                                    <small class="text-muted">Réinitialisation</small>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editTemplate('password')">
                                        <i class="feather icon-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="previewTemplate('password')">
                                        <i class="feather icon-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <strong>Alerte système</strong><br>
                                    <small class="text-muted">Maintenance</small>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editTemplate('alert')">
                                        <i class="feather icon-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="previewTemplate('alert')">
                                        <i class="feather icon-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="text-center mt-3">
                    <button class="btn btn-success btn-sm" onclick="addTemplate()">
                        <i class="feather icon-plus me-1"></i>Nouveau modèle
                    </button>
                </div>
            </div>
        </div>

        <!-- Test de notifications -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="feather icon-send text-c-green me-2"></i>Test de Notifications</h5>
            </div>
            <div class="card-block">
                <form method="POST" action="{{ route('notifications.test') }}">
                    @csrf
                    <div class="mb-3">
                        <label for="test_email" class="form-label">Email de test</label>
                        <input type="email" class="form-control" id="test_email" name="test_email" 
                               value="{{ auth()->user()->email }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="test_template" class="form-label">Modèle à tester</label>
                        <select class="form-control" id="test_template" name="test_template" required>
                            <option value="welcome">Bienvenue</option>
                            <option value="password">Mot de passe</option>
                            <option value="alert">Alerte système</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success btn-block">
                        <i class="feather icon-send me-2"></i>Envoyer test
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'édition de modèle -->
<div class="modal fade" id="editTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifier le modèle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editTemplateForm">
                    <div class="mb-3">
                        <label for="template_name" class="form-label">Nom du modèle</label>
                        <input type="text" class="form-control" id="template_name" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="template_subject" class="form-label">Sujet</label>
                        <input type="text" class="form-control" id="template_subject">
                    </div>
                    <div class="mb-3">
                        <label for="template_content" class="form-label">Contenu</label>
                        <textarea class="form-control" id="template_content" rows="10"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="saveTemplate()">Sauvegarder</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function resetForm() {
    if (confirm('Réinitialiser tous les paramètres ?')) {
        document.querySelector('form').reset();
    }
}

function editTemplate(templateType) {
    // Simuler le chargement du modèle
    const templates = {
        'welcome': {
            name: 'Bienvenue',
            subject: 'Bienvenue dans CRFM',
            content: 'Bonjour {{name}},\n\nBienvenue dans le système CRFM...'
        },
        'password': {
            name: 'Mot de passe',
            subject: 'Réinitialisation de votre mot de passe',
            content: 'Bonjour {{name}},\n\nVous avez demandé la réinitialisation...'
        },
        'alert': {
            name: 'Alerte système',
            subject: 'Alerte système CRFM',
            content: 'Une maintenance système est prévue...'
        }
    };

    const template = templates[templateType];
    if (template) {
        document.getElementById('template_name').value = template.name;
        document.getElementById('template_subject').value = template.subject;
        document.getElementById('template_content').value = template.content;
        
        new bootstrap.Modal(document.getElementById('editTemplateModal')).show();
    }
}

function previewTemplate(templateType) {
    alert('Aperçu du modèle : ' + templateType + '\n(Fonctionnalité en développement)');
}

function addTemplate() {
    alert('Ajouter un nouveau modèle\n(Fonctionnalité en développement)');
}

function saveTemplate() {
    alert('Modèle sauvegardé avec succès !');
    bootstrap.Modal.getInstance(document.getElementById('editTemplateModal')).hide();
}
</script>
@endpush

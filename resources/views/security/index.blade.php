@extends('layouts.template')

@section('title', 'Paramètres de Sécurité - CRFM')

@section('page-title', 'Paramètres de Sécurité')
@section('page-description', 'Configuration de la sécurité et des politiques d\'accès')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('parametres.index') }}">Paramètres</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Sécurité</a></li>
    </ul>
@endsection

@section('content')
<div class="row">
    <!-- Score de sécurité -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-shield text-c-blue me-2"></i>Score de sécurité</h5>
            </div>
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="security-score">
                            <div class="progress mb-3" style="height: 20px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 85%" id="securityScore">
                                    85%
                                </div>
                            </div>
                            <p class="text-muted">Votre système a un bon niveau de sécurité. Quelques améliorations sont possibles.</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-info" onclick="generateSecurityReport()">
                            <i class="feather icon-file-text me-2"></i>Rapport de sécurité
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<form method="POST" action="{{ route('security.update') }}" class="needs-validation" novalidate>
    @csrf
    @method('PUT')
    
    <div class="row">
        <!-- Politique des mots de passe -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-key text-c-green me-2"></i>Politique des mots de passe</h5>
                </div>
                <div class="card-block">
                    <div class="form-group">
                        <label class="required">Longueur minimale</label>
                        <input type="number" name="password_min_length" class="form-control {{ $errors->has('password_min_length') ? 'is-invalid' : '' }}" 
                               value="{{ old('password_min_length', $settings['password_min_length']) }}" min="6" max="50" required>
                        @if($errors->has('password_min_length'))
                            <div class="invalid-feedback">{{ $errors->first('password_min_length') }}</div>
                        @endif
                    </div>
                    
                    <div class="form-group">
                        <label>Exigences de complexité</label>
                        <div class="form-check">
                            <input type="checkbox" name="password_require_uppercase" class="form-check-input" id="uppercase" 
                                   value="1" {{ $settings['password_require_uppercase'] ? 'checked' : '' }}>
                            <label class="form-check-label" for="uppercase">
                                Majuscules requises (A-Z)
                            </label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" name="password_require_lowercase" class="form-check-input" id="lowercase" 
                                   value="1" {{ $settings['password_require_lowercase'] ? 'checked' : '' }}>
                            <label class="form-check-label" for="lowercase">
                                Minuscules requises (a-z)
                            </label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" name="password_require_numbers" class="form-check-input" id="numbers" 
                                   value="1" {{ $settings['password_require_numbers'] ? 'checked' : '' }}>
                            <label class="form-check-label" for="numbers">
                                Chiffres requis (0-9)
                            </label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" name="password_require_symbols" class="form-check-input" id="symbols" 
                                   value="1" {{ $settings['password_require_symbols'] ? 'checked' : '' }}>
                            <label class="form-check-label" for="symbols">
                                Symboles requis (!@#$%^&*)
                            </label>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Expiration (jours)</label>
                                <input type="number" name="password_expiry_days" class="form-control {{ $errors->has('password_expiry_days') ? 'is-invalid' : '' }}" 
                                       value="{{ old('password_expiry_days', $settings['password_expiry_days']) }}" min="0" max="365">
                                <small class="form-text text-muted">0 = pas d'expiration</small>
                                @if($errors->has('password_expiry_days'))
                                    <div class="invalid-feedback">{{ $errors->first('password_expiry_days') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Historique</label>
                                <input type="number" name="password_history_count" class="form-control {{ $errors->has('password_history_count') ? 'is-invalid' : '' }}" 
                                       value="{{ old('password_history_count', $settings['password_history_count']) }}" min="0" max="10">
                                <small class="form-text text-muted">Anciens mots de passe interdits</small>
                                @if($errors->has('password_history_count'))
                                    <div class="invalid-feedback">{{ $errors->first('password_history_count') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contrôle d'accès -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-lock text-c-orange me-2"></i>Contrôle d'accès</h5>
                </div>
                <div class="card-block">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Tentatives max</label>
                                <input type="number" name="max_login_attempts" class="form-control {{ $errors->has('max_login_attempts') ? 'is-invalid' : '' }}" 
                                       value="{{ old('max_login_attempts', $settings['max_login_attempts']) }}" min="3" max="10" required>
                                @if($errors->has('max_login_attempts'))
                                    <div class="invalid-feedback">{{ $errors->first('max_login_attempts') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Verrouillage (min)</label>
                                <input type="number" name="lockout_duration" class="form-control {{ $errors->has('lockout_duration') ? 'is-invalid' : '' }}" 
                                       value="{{ old('lockout_duration', $settings['lockout_duration']) }}" min="5" max="1440" required>
                                @if($errors->has('lockout_duration'))
                                    <div class="invalid-feedback">{{ $errors->first('lockout_duration') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="required">Durée de session (min)</label>
                        <input type="number" name="session_lifetime" class="form-control {{ $errors->has('session_lifetime') ? 'is-invalid' : '' }}" 
                               value="{{ old('session_lifetime', $settings['session_lifetime']) }}" min="15" max="1440" required>
                        @if($errors->has('session_lifetime'))
                            <div class="invalid-feedback">{{ $errors->first('session_lifetime') }}</div>
                        @endif
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" name="auto_logout_inactive" class="form-check-input" id="auto_logout" 
                                   value="1" {{ $settings['auto_logout_inactive'] ? 'checked' : '' }}>
                            <label class="form-check-label" for="auto_logout">
                                Déconnexion automatique
                            </label>
                        </div>
                        <div class="mt-2" id="inactive_timeout_group" style="{{ $settings['auto_logout_inactive'] ? '' : 'display: none;' }}">
                            <label>Timeout d'inactivité (min)</label>
                            <input type="number" name="inactive_timeout" class="form-control" 
                                   value="{{ old('inactive_timeout', $settings['inactive_timeout']) }}" min="5" max="120">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" name="force_https" class="form-check-input" id="force_https" 
                                   value="1" {{ $settings['force_https'] ? 'checked' : '' }}>
                            <label class="form-check-label" for="force_https">
                                Forcer HTTPS
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" name="two_factor_enabled" class="form-check-input" id="two_factor" 
                                   value="1" {{ $settings['two_factor_enabled'] ? 'checked' : '' }}>
                            <label class="form-check-label" for="two_factor">
                                Authentification à deux facteurs
                            </label>
                        </div>
                        <small class="form-text text-muted">Nécessite une configuration supplémentaire</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Actions de sécurité -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-zap text-c-red me-2"></i>Actions de sécurité</h5>
                </div>
                <div class="card-block">
                    <div class="row">
                        <div class="col-md-8">
                            <p class="text-muted">Actions critiques qui affectent tous les utilisateurs du système.</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-warning" onclick="forcePasswordReset()">
                                    <i class="feather icon-key me-2"></i>Forcer reset MDP
                                </button>
                                <button type="button" class="btn btn-danger" onclick="clearAllSessions()">
                                    <i class="feather icon-log-out me-2"></i>Fermer sessions
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Boutons d'action -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-block text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="feather icon-save me-2"></i>Enregistrer les paramètres
                    </button>
                    <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="resetForm()">
                        <i class="feather icon-refresh-cw me-2"></i>Réinitialiser
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Modal de confirmation pour actions critiques -->
<div class="modal fade" id="confirmActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer l'action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="feather icon-alert-triangle me-2"></i>
                    <strong>Attention !</strong> Cette action est irréversible et affectera tous les utilisateurs.
                </div>
                <p id="confirmActionText"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="confirmActionBtn">Confirmer</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.required::after {
    content: " *";
    color: red;
}
.form-group {
    margin-bottom: 1rem;
}
.card-block {
    padding: 1.5rem;
}
.security-score .progress {
    background-color: #e9ecef;
}
.form-check {
    margin-bottom: 0.5rem;
}
</style>
@endpush

@push('scripts')
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Gestion de l'affichage conditionnel
document.addEventListener('DOMContentLoaded', function() {
    const autoLogoutCheckbox = document.getElementById('auto_logout');
    const inactiveTimeoutGroup = document.getElementById('inactive_timeout_group');

    if (autoLogoutCheckbox) {
        autoLogoutCheckbox.addEventListener('change', function() {
            inactiveTimeoutGroup.style.display = this.checked ? 'block' : 'none';
        });
    }
});

function resetForm() {
    if (confirm('Réinitialiser tous les champs ?')) {
        document.querySelector('form').reset();
    }
}

function generateSecurityReport() {
    fetch('{{ route("security.report") }}', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        // Afficher le rapport dans une modal ou télécharger
        const reportContent = `
            Rapport de Sécurité - ${new Date().toLocaleDateString()}

            Utilisateurs totaux: ${data.total_users}
            Utilisateurs actifs: ${data.active_users}
            Administrateurs: ${data.admin_users}
            Connexions récentes: ${data.recent_logins}
            Tentatives échouées: ${data.failed_attempts}
            Sessions actives: ${data.active_sessions}
            Score de sécurité: ${data.security_score}/100
        `;

        const blob = new Blob([reportContent], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'rapport_securite_' + new Date().toISOString().split('T')[0] + '.txt';
        a.click();
        window.URL.revokeObjectURL(url);
    })
    .catch(error => {
        CRFM.showToast('Erreur lors de la génération du rapport', 'error');
    });
}

function forcePasswordReset() {
    showConfirmModal(
        'Forcer la réinitialisation des mots de passe',
        'Tous les utilisateurs devront changer leur mot de passe à leur prochaine connexion.',
        function() {
            fetch('{{ route("security.force-password-reset") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                CRFM.showToast('Réinitialisation forcée activée', 'success');
            })
            .catch(error => {
                CRFM.showToast('Erreur lors de la réinitialisation', 'error');
            });
        }
    );
}

function clearAllSessions() {
    showConfirmModal(
        'Fermer toutes les sessions',
        'Tous les utilisateurs connectés seront déconnectés immédiatement.',
        function() {
            fetch('{{ route("security.clear-sessions") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                CRFM.showToast('Toutes les sessions ont été fermées', 'success');
            })
            .catch(error => {
                CRFM.showToast('Erreur lors de la fermeture des sessions', 'error');
            });
        }
    );
}

function showConfirmModal(title, message, callback) {
    document.querySelector('#confirmActionModal .modal-title').textContent = title;
    document.getElementById('confirmActionText').textContent = message;

    const confirmBtn = document.getElementById('confirmActionBtn');
    confirmBtn.onclick = function() {
        callback();
        bootstrap.Modal.getInstance(document.getElementById('confirmActionModal')).hide();
    };

    const modal = new bootstrap.Modal(document.getElementById('confirmActionModal'));
    modal.show();
}
</script>
@endpush

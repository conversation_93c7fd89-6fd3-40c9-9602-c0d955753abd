@extends('layouts.template')

@section('title', 'Gestion des Notifications - CRFM')

@section('page-title', 'Gestion des Notifications')
@section('page-description', 'Configuration des notifications email, SMS et système')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('parametres.index') }}">Paramètres</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Notifications</a></li>
    </ul>
@endsection

@section('content')
<div class="row">
    <!-- Navigation des onglets -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="notificationTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                            <i class="feather icon-settings me-2"></i>Configuration
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="templates-tab" data-bs-toggle="tab" data-bs-target="#templates" type="button" role="tab">
                            <i class="feather icon-file-text me-2"></i>Modèles
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="bulk-tab" data-bs-toggle="tab" data-bs-target="#bulk" type="button" role="tab">
                            <i class="feather icon-send me-2"></i>Envoi en masse
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">
                            <i class="feather icon-clock me-2"></i>Historique
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="tab-content" id="notificationTabContent">
    <!-- Onglet Configuration -->
    <div class="tab-pane fade show active" id="settings" role="tabpanel">
        <form method="POST" action="{{ route('notifications.update') }}" class="needs-validation" novalidate>
            @csrf
            @method('PUT')
            
            <div class="row">
                <!-- Configuration Email -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-mail text-c-blue me-2"></i>Configuration Email</h5>
                            <div class="card-header-right">
                                <div class="form-check form-switch">
                                    <input type="checkbox" name="email_enabled" class="form-check-input" id="email_enabled" 
                                           value="1" {{ $settings['email_enabled'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="email_enabled">Activé</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-block">
                            <div class="form-group">
                                <label class="required">Nom de l'expéditeur</label>
                                <input type="text" name="email_from_name" class="form-control {{ $errors->has('email_from_name') ? 'is-invalid' : '' }}"
                                       value="{{ old('email_from_name', $settings['email_from_name']) }}" required>
                                @if($errors->has('email_from_name'))
                                    <div class="invalid-feedback">{{ $errors->first('email_from_name') }}</div>
                                @endif
                            </div>
                            
                            <div class="form-group">
                                <label class="required">Email de l'expéditeur</label>
                                <input type="email" name="email_from_address" class="form-control {{ $errors->has('email_from_address') ? 'is-invalid' : '' }}" 
                                       value="{{ old('email_from_address', $settings['email_from_address']) }}" required>
                                @if($errors->has('email_from_address'))
                                    <div class="invalid-feedback">{{ $errors->first('email_from_address') }}</div>
                                @endif
                            </div>
                            
                            <div class="form-group">
                                <label>Email de réponse</label>
                                <input type="email" name="email_reply_to" class="form-control {{ $errors->has('email_reply_to') ? 'is-invalid' : '' }}" 
                                       value="{{ old('email_reply_to', $settings['email_reply_to']) }}">
                                @if($errors->has('email_reply_to'))
                                    <div class="invalid-feedback">{{ $errors->first('email_reply_to') }}</div>
                                @endif
                            </div>
                            
                            <div class="form-group">
                                <label>Tester la configuration</label>
                                <div class="input-group">
                                    <input type="email" id="test_email" class="form-control" placeholder="<EMAIL>">
                                    <button type="button" class="btn btn-info" onclick="testEmail()">
                                        <i class="feather icon-send me-1"></i>Tester
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Configuration SMS -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-smartphone text-c-green me-2"></i>Configuration SMS</h5>
                            <div class="card-header-right">
                                <div class="form-check form-switch">
                                    <input type="checkbox" name="sms_enabled" class="form-check-input" id="sms_enabled" 
                                           value="1" {{ $settings['sms_enabled'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="sms_enabled">Activé</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-block">
                            <div class="form-group">
                                <label>Fournisseur SMS</label>
                                <select name="sms_provider" class="form-control {{ $errors->has('sms_provider') ? 'is-invalid' : '' }}">
                                    <option value="">Sélectionner un fournisseur</option>
                                    <option value="orange" {{ $settings['sms_provider'] == 'orange' ? 'selected' : '' }}>Orange Mali</option>
                                    <option value="twilio" {{ $settings['sms_provider'] == 'twilio' ? 'selected' : '' }}>Twilio</option>
                                    <option value="nexmo" {{ $settings['sms_provider'] == 'nexmo' ? 'selected' : '' }}>Nexmo</option>
                                </select>
                                @if($errors->has('sms_provider'))
                                    <div class="invalid-feedback">{{ $errors->first('sms_provider') }}</div>
                                @endif
                            </div>
                            
                            <div class="form-group">
                                <label>Clé API</label>
                                <input type="text" name="sms_api_key" class="form-control {{ $errors->has('sms_api_key') ? 'is-invalid' : '' }}" 
                                       value="{{ old('sms_api_key', $settings['sms_api_key']) }}">
                                @if($errors->has('sms_api_key'))
                                    <div class="invalid-feedback">{{ $errors->first('sms_api_key') }}</div>
                                @endif
                            </div>
                            
                            <div class="form-group">
                                <label>Secret API</label>
                                <input type="password" name="sms_api_secret" class="form-control {{ $errors->has('sms_api_secret') ? 'is-invalid' : '' }}" 
                                       value="{{ old('sms_api_secret', $settings['sms_api_secret']) }}">
                                @if($errors->has('sms_api_secret'))
                                    <div class="invalid-feedback">{{ $errors->first('sms_api_secret') }}</div>
                                @endif
                            </div>
                            
                            <div class="form-group">
                                <label>ID de l'expéditeur</label>
                                <input type="text" name="sms_sender_id" class="form-control {{ $errors->has('sms_sender_id') ? 'is-invalid' : '' }}" 
                                       value="{{ old('sms_sender_id', $settings['sms_sender_id']) }}" maxlength="11">
                                <small class="form-text text-muted">Maximum 11 caractères</small>
                                @if($errors->has('sms_sender_id'))
                                    <div class="invalid-feedback">{{ $errors->first('sms_sender_id') }}</div>
                                @endif
                            </div>
                            
                            <div class="form-group">
                                <label>Tester la configuration</label>
                                <div class="input-group">
                                    <input type="text" id="test_phone" class="form-control" placeholder="+223 70 00 00 00">
                                    <button type="button" class="btn btn-success" onclick="testSms()">
                                        <i class="feather icon-send me-1"></i>Tester
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Paramètres généraux -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-sliders text-c-orange me-2"></i>Paramètres généraux</h5>
                        </div>
                        <div class="card-block">
                            <div class="form-group">
                                <label class="required">Fréquence d'envoi</label>
                                <select name="notification_frequency" class="form-control {{ $errors->has('notification_frequency') ? 'is-invalid' : '' }}" required>
                                    <option value="immediate" {{ $settings['notification_frequency'] == 'immediate' ? 'selected' : '' }}>Immédiat</option>
                                    <option value="hourly" {{ $settings['notification_frequency'] == 'hourly' ? 'selected' : '' }}>Toutes les heures</option>
                                    <option value="daily" {{ $settings['notification_frequency'] == 'daily' ? 'selected' : '' }}>Quotidien</option>
                                    <option value="weekly" {{ $settings['notification_frequency'] == 'weekly' ? 'selected' : '' }}>Hebdomadaire</option>
                                </select>
                                @if($errors->has('notification_frequency'))
                                    <div class="invalid-feedback">{{ $errors->first('notification_frequency') }}</div>
                                @endif
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="required">Max par utilisateur</label>
                                        <input type="number" name="max_notifications_per_user" class="form-control {{ $errors->has('max_notifications_per_user') ? 'is-invalid' : '' }}" 
                                               value="{{ old('max_notifications_per_user', $settings['max_notifications_per_user']) }}" min="1" max="100" required>
                                        @if($errors->has('max_notifications_per_user'))
                                    <div class="invalid-feedback">{{ $errors->first('max_notifications_per_user') }}</div>
                                @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="required">Rétention (jours)</label>
                                        <input type="number" name="notification_retention_days" class="form-control {{ $errors->has('notification_retention_days') ? 'is-invalid' : '' }}" 
                                               value="{{ old('notification_retention_days', $settings['notification_retention_days']) }}" min="1" max="365" required>
                                        @if($errors->has('notification_retention_days'))
                                    <div class="invalid-feedback">{{ $errors->first('notification_retention_days') }}</div>
                                @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Types de notifications -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="feather icon-bell text-c-purple me-2"></i>Types de notifications</h5>
                        </div>
                        <div class="card-block">
                            <div class="form-check">
                                <input type="checkbox" name="notify_new_adherent" class="form-check-input" id="notify_new_adherent" 
                                       value="1" {{ $settings['notify_new_adherent'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="notify_new_adherent">
                                    <strong>Nouvel adhérent</strong><br>
                                    <small class="text-muted">Notification lors de la création d'un dossier</small>
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input type="checkbox" name="notify_cotisation_due" class="form-check-input" id="notify_cotisation_due" 
                                       value="1" {{ $settings['notify_cotisation_due'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="notify_cotisation_due">
                                    <strong>Cotisation échue</strong><br>
                                    <small class="text-muted">Rappel automatique pour les retards</small>
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input type="checkbox" name="notify_pension_request" class="form-check-input" id="notify_pension_request" 
                                       value="1" {{ $settings['notify_pension_request'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="notify_pension_request">
                                    <strong>Demande de pension</strong><br>
                                    <small class="text-muted">Nouvelle demande de pension</small>
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input type="checkbox" name="notify_system_error" class="form-check-input" id="notify_system_error" 
                                       value="1" {{ $settings['notify_system_error'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="notify_system_error">
                                    <strong>Erreur système</strong><br>
                                    <small class="text-muted">Alerte en cas d'erreur critique</small>
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input type="checkbox" name="notify_backup_status" class="form-check-input" id="notify_backup_status" 
                                       value="1" {{ $settings['notify_backup_status'] ? 'checked' : '' }}>
                                <label class="form-check-label" for="notify_backup_status">
                                    <strong>Statut de sauvegarde</strong><br>
                                    <small class="text-muted">Rapport quotidien des sauvegardes</small>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Boutons d'action -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-block text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="feather icon-save me-2"></i>Enregistrer la configuration
                            </button>
                            <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="resetForm()">
                                <i class="feather icon-refresh-cw me-2"></i>Réinitialiser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Onglet Modèles -->
    <div class="tab-pane fade" id="templates" role="tabpanel">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="feather icon-file-text text-c-blue me-2"></i>Modèles de notifications</h5>
                        <div class="card-header-right">
                            <button class="btn btn-primary btn-sm" onclick="createNewTemplate()">
                                <i class="feather icon-plus me-1"></i>Nouveau modèle
                            </button>
                        </div>
                    </div>
                    <div class="card-block">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Sujet</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($templates as $key => $template)
                                    <tr>
                                        <td><strong>{{ $template['name'] }}</strong></td>
                                        <td>{{ $template['subject'] }}</td>
                                        <td><small class="text-muted">{{ $template['description'] }}</small></td>
                                        <td>
                                            <button class="btn btn-warning btn-sm" onclick="editTemplate('{{ $key }}')">
                                                <i class="feather icon-edit"></i>
                                            </button>
                                            <button class="btn btn-info btn-sm" onclick="previewTemplate('{{ $key }}')">
                                                <i class="feather icon-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Onglet Envoi en masse -->
    <div class="tab-pane fade" id="bulk" role="tabpanel">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="feather icon-send text-c-green me-2"></i>Envoi de notification en masse</h5>
                    </div>
                    <div class="card-block">
                        <form method="POST" action="{{ route('notifications.bulk') }}">
                            @csrf
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="required">Destinataires</label>
                                        <select name="recipients" class="form-control" required>
                                            <option value="">Sélectionner les destinataires</option>
                                            <option value="all">Tous les utilisateurs</option>
                                            <option value="admins">Administrateurs uniquement</option>
                                            <option value="gestionnaires">Gestionnaires uniquement</option>
                                            <option value="operateurs">Opérateurs uniquement</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="required">Type d'envoi</label>
                                        <select name="type" class="form-control" required>
                                            <option value="email">Email uniquement</option>
                                            <option value="sms">SMS uniquement</option>
                                            <option value="both">Email et SMS</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="required">Sujet</label>
                                <input type="text" name="subject" class="form-control" required maxlength="255">
                            </div>

                            <div class="form-group">
                                <label class="required">Message</label>
                                <textarea name="message" class="form-control" rows="5" required maxlength="1000"></textarea>
                                <small class="form-text text-muted">Maximum 1000 caractères</small>
                            </div>

                            <div class="form-group">
                                <label class="required">Priorité</label>
                                <select name="priority" class="form-control" required>
                                    <option value="low">Basse</option>
                                    <option value="normal" selected>Normale</option>
                                    <option value="high">Haute</option>
                                    <option value="urgent">Urgente</option>
                                </select>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="feather icon-send me-2"></i>Envoyer la notification
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="feather icon-info text-c-blue me-2"></i>Informations</h5>
                    </div>
                    <div class="card-block">
                        <div class="alert alert-info">
                            <i class="feather icon-info me-2"></i>
                            Les notifications en masse sont envoyées en arrière-plan pour éviter les timeouts.
                        </div>

                        <h6>Statistiques</h6>
                        <ul class="list-unstyled">
                            <li><strong>Total utilisateurs :</strong> {{ App\Models\User::count() }}</li>
                            <li><strong>Administrateurs :</strong> {{ App\Models\User::where('role', 'admin')->count() }}</li>
                            <li><strong>Gestionnaires :</strong> {{ App\Models\User::where('role', 'gestionnaire')->count() }}</li>
                            <li><strong>Opérateurs :</strong> {{ App\Models\User::where('role', 'operateur')->count() }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Onglet Historique -->
    <div class="tab-pane fade" id="history" role="tabpanel">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="feather icon-clock text-c-purple me-2"></i>Historique des notifications</h5>
                        <div class="card-header-right">
                            <button class="btn btn-secondary btn-sm">
                                <i class="feather icon-download me-1"></i>Exporter
                            </button>
                        </div>
                    </div>
                    <div class="card-block">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>Destinataire</th>
                                        <th>Sujet</th>
                                        <th>Statut</th>
                                        <th>Priorité</th>
                                        <th>Envoyé le</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentNotifications as $notification)
                                    <tr>
                                        <td>
                                            <span class="badge bg-{{ $notification['type'] === 'email' ? 'primary' : 'success' }}">
                                                {{ strtoupper($notification['type']) }}
                                            </span>
                                        </td>
                                        <td>{{ $notification['recipient'] }}</td>
                                        <td>{{ $notification['subject'] }}</td>
                                        <td>
                                            <span class="badge bg-{{ $notification['status'] === 'sent' ? 'success' : 'danger' }}">
                                                {{ $notification['status'] === 'sent' ? 'Envoyé' : 'Échec' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{
                                                $notification['priority'] === 'urgent' ? 'danger' :
                                                ($notification['priority'] === 'high' ? 'warning' :
                                                ($notification['priority'] === 'normal' ? 'info' : 'secondary'))
                                            }}">
                                                {{ ucfirst($notification['priority']) }}
                                            </span>
                                        </td>
                                        <td>{{ $notification['sent_at']->format('d/m/Y H:i') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.required::after {
    content: " *";
    color: red;
}
.form-group {
    margin-bottom: 1rem;
}
.card-block {
    padding: 1.5rem;
}
.form-check {
    margin-bottom: 1rem;
    padding: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}
.form-check:hover {
    background-color: #f8f9fa;
}
.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
}
.nav-tabs .nav-link.active {
    border-bottom-color: #007bff;
    background-color: transparent;
}
.tab-content {
    padding-top: 1rem;
}
</style>
@endpush

@push('scripts')
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

function resetForm() {
    if (confirm('Réinitialiser tous les champs ?')) {
        document.querySelector('form').reset();
    }
}

function testEmail() {
    const email = document.getElementById('test_email').value;
    if (!email) {
        CRFM.showToast('Veuillez saisir une adresse email', 'warning');
        return;
    }

    fetch('{{ route("notifications.test-email") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ test_email: email })
    })
    .then(response => response.json())
    .then(data => {
        CRFM.showToast('Email de test envoyé avec succès', 'success');
    })
    .catch(error => {
        CRFM.showToast('Erreur lors de l'envoi de l'email', 'error');
    });
}

function testSms() {
    const phone = document.getElementById('test_phone').value;
    if (!phone) {
        CRFM.showToast('Veuillez saisir un numéro de téléphone', 'warning');
        return;
    }

    fetch('{{ route("notifications.test-sms") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ test_phone: phone })
    })
    .then(response => response.json())
    .then(data => {
        CRFM.showToast('SMS de test envoyé avec succès', 'success');
    })
    .catch(error => {
        CRFM.showToast('Erreur lors de l'envoi du SMS', 'error');
    });
}

function createNewTemplate() {
    // Créer un modal pour le nouveau modèle
    const modalHtml = `
        <div class="modal fade" id="newTemplateModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Nouveau modèle de notification</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="newTemplateForm">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">Nom du modèle</label>
                                <input type="text" name="name" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Sujet</label>
                                <input type="text" name="subject" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Type</label>
                                <select name="type" class="form-control" required>
                                    <option value="email">Email</option>
                                    <option value="sms">SMS</option>
                                    <option value="push">Push</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Contenu</label>
                                <textarea name="content" class="form-control" rows="5" required
                                          placeholder="Contenu du modèle de notification..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">Créer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // Supprimer le modal existant s'il y en a un
    const existingModal = document.getElementById('newTemplateModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Ajouter le nouveau modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Ajouter le gestionnaire d'événement pour le formulaire
    const form = document.getElementById('newTemplateForm');
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="feather icon-loader me-1"></i>Création...';

        // Préparer les données
        const formData = new FormData(this);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        // Envoyer la requête
        fetch('{{ route("notifications.templates.create") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.json().catch(() => ({ success: true }));
            }
            throw new Error('Erreur réseau');
        })
        .then(data => {
            if (typeof CRFM !== 'undefined' && CRFM.showToast) {
                CRFM.showToast('Modèle créé avec succès', 'success');
            } else {
                alert('Modèle créé avec succès');
            }

            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('newTemplateModal'));
            if (modal) {
                modal.hide();
            }

            // Recharger la page
            setTimeout(() => {
                location.reload();
            }, 1000);
        })
        .catch(error => {
            console.error('Erreur:', error);
            if (typeof CRFM !== 'undefined' && CRFM.showToast) {
                CRFM.showToast('Erreur lors de la création du modèle', 'error');
            } else {
                alert('Erreur lors de la création du modèle');
            }
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('newTemplateModal'));
    modal.show();
}

function editTemplate(templateKey) {
    // Récupérer les données du modèle depuis les templates disponibles
    const templates = @json($templates ?? []);
    const template = templates[templateKey];

    if (!template) {
        if (typeof CRFM !== 'undefined' && CRFM.showToast) {
            CRFM.showToast('Modèle non trouvé', 'error');
        } else {
            alert('Modèle non trouvé');
        }
        return;
    }

    // Créer un modal pour éditer le modèle
    const modalHtml = `
        <div class="modal fade" id="editTemplateModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Modifier le modèle: ${template.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="editTemplateForm">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">Nom du modèle</label>
                                <input type="text" name="name" class="form-control" value="${template.name}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Sujet</label>
                                <input type="text" name="subject" class="form-control" value="${template.subject}" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Contenu</label>
                                <textarea name="content" class="form-control" rows="5" required>${template.content || 'Contenu du modèle...'}</textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">Sauvegarder</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // Supprimer le modal existant s'il y en a un
    const existingModal = document.getElementById('editTemplateModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Ajouter le nouveau modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Ajouter le gestionnaire d'événement pour le formulaire
    const form = document.getElementById('editTemplateForm');
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="feather icon-loader me-1"></i>Sauvegarde...';

        // Préparer les données
        const formData = new FormData(this);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        formData.append('_method', 'PUT');

        // Envoyer la requête
        fetch(`/parametres/notifications/templates/${templateKey}`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.json().catch(() => ({ success: true }));
            }
            throw new Error('Erreur réseau');
        })
        .then(data => {
            if (typeof CRFM !== 'undefined' && CRFM.showToast) {
                CRFM.showToast('Modèle mis à jour avec succès', 'success');
            } else {
                alert('Modèle mis à jour avec succès');
            }

            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editTemplateModal'));
            if (modal) {
                modal.hide();
            }

            // Recharger la page
            setTimeout(() => {
                location.reload();
            }, 1000);
        })
        .catch(error => {
            console.error('Erreur:', error);
            if (typeof CRFM !== 'undefined' && CRFM.showToast) {
                CRFM.showToast('Erreur lors de la mise à jour du modèle', 'error');
            } else {
                alert('Erreur lors de la mise à jour du modèle');
            }
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('editTemplateModal'));
    modal.show();
}

function previewTemplate(templateKey) {
    // Récupérer les données du modèle depuis les templates disponibles
    const templates = @json($templates ?? []);
    const template = templates[templateKey];

    if (!template) {
        if (typeof CRFM !== 'undefined' && CRFM.showToast) {
            CRFM.showToast('Modèle non trouvé', 'error');
        } else {
            alert('Modèle non trouvé');
        }
        return;
    }

    // Créer un modal pour prévisualiser le modèle
    const modalHtml = `
        <div class="modal fade" id="previewTemplateModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Aperçu du modèle: ${template.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <strong>Modèle :</strong> ${template.name}<br>
                            <strong>Type :</strong> Email<br>
                            <strong>Description :</strong> ${template.description || 'Aucune description'}
                        </div>
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="feather icon-mail me-2"></i>
                                    Sujet : ${template.subject}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="email-preview">
                                    <p><strong>De :</strong> CRFM &lt;<EMAIL>&gt;</p>
                                    <p><strong>À :</strong> <EMAIL></p>
                                    <hr>
                                    <div class="email-content">
                                        ${template.content ? template.content.replace(/\\n/g, '<br>') : 'Contenu du modèle de notification...'}
                                    </div>
                                    <hr>
                                    <small class="text-muted">
                                        <i class="feather icon-info me-1"></i>
                                        Ceci est un aperçu. Les variables seront remplacées lors de l'envoi réel.
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <h6>Variables disponibles :</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><code>{<!-- -->{nom_utilisateur}<!-- -->}</code> - Nom de l'utilisateur</li>
                                        <li><code>{<!-- -->{email_utilisateur}<!-- -->}</code> - Email de l'utilisateur</li>
                                        <li><code>{<!-- -->{date_actuelle}<!-- -->}</code> - Date actuelle</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><code>{<!-- -->{nom_systeme}<!-- -->}</code> - Nom du système</li>
                                        <li><code>{<!-- -->{url_systeme}<!-- -->}</code> - URL du système</li>
                                        <li><code>{<!-- -->{signature}<!-- -->}</code> - Signature automatique</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="editTemplate('${templateKey}')">
                            <i class="feather icon-edit me-1"></i>Modifier
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Supprimer le modal existant s'il y en a un
    const existingModal = document.getElementById('previewTemplateModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Ajouter le nouveau modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('previewTemplateModal'));
    modal.show();
}

// Sauvegarde automatique des onglets
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('#notificationTabs button[data-bs-toggle="tab"]');

    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            localStorage.setItem('activeNotificationTab', e.target.id);
        });
    });

    // Restaurer l'onglet actif
    const activeTab = localStorage.getItem('activeNotificationTab');
    if (activeTab) {
        const tabButton = document.getElementById(activeTab);
        if (tabButton) {
            const tab = new bootstrap.Tab(tabButton);
            tab.show();
        }
    }
});
</script>
@endpush

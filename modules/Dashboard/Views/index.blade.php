@extends('layouts.template')

@section('title', 'Tableau de bord - CRFM')

@push('styles')
    <!-- Chart.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chartist@0.11.4/dist/chartist.min.css" type="text/css">
    <style>
        .welcome-card {
            background: linear-gradient(to right, #1a237e, #283593);
            color: white;
            border-radius: 5px;
        }
        .welcome-card h2, .welcome-card p {
            color: white;
        }
        .dashboard-stats .card h4 {
            font-family: 'Poppins', sans-serif !important;
            font-weight: 700 !important;
            font-size: 2.2rem !important;
        }
        .dashboard-stats .card h6 {
            font-family: 'Inter', sans-serif !important;
            font-weight: 500 !important;
            font-size: 0.875rem !important;
            text-transform: uppercase !important;
        }
        .card {
            border-radius: 12px !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
            border: none !important;
        }
        .dashboard-stats .card:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
        }
        @media (max-width: 768px) {
            .dashboard-stats .card h4 {
                font-size: 1.5rem !important;
            }
            .dashboard-stats .card h6 {
                font-size: 0.8rem !important;
            }
            .welcome-card h2 {
                font-size: 1.5rem !important;
            }
        }
    </style>
@endpush

@section('page-title', 'Tableau de bord')
@section('page-description', 'Vue d\'ensemble des activités et statistiques')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Tableau de bord</a></li>
    </ul>
@endsection

@section('content')
<!-- Dashboard Header -->
<div class="row dashboard-header">
    <div class="col-md-12">
        <div class="card welcome-card">
            <div class="card-block">
                <div class="row">
                    <div class="col-md-8">
                        <h2>Bienvenue, {{ auth()->user()->name }} !</h2>
                        <p class="mb-0">Tableau de bord de gestion des cotisations et pré-liquidations de pension</p>
                        <p>Date du jour : <span id="current-date">{{ now()->format('d/m/Y') }}</span></p>
                    </div>
                    <div class="col-md-4 text-end d-flex align-items-center justify-content-end">
                        <button class="btn btn-light me-2" onclick="location.reload()"><i class="feather icon-refresh-cw"></i> Actualiser</button>
                        <button class="btn btn-light" onclick="window.print()"><i class="feather icon-printer"></i> Imprimer</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row dashboard-stats">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-blue text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600" style="color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;">{{ isset($stats['adherents_actifs']) ? number_format($stats['adherents_actifs']) : '0' }}</h4>
                        <h6 class="text-white m-b-0" style="color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;">Adhérents actifs</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-users f-28 text-white" style="color: #ffffff !important;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-green text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600" style="color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;">{{ isset($stats['adherents_retraites']) ? number_format($stats['adherents_retraites']) : '0' }}</h4>
                        <h6 class="text-white m-b-0" style="color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;">Retraités</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-award f-28 text-white" style="color: #ffffff !important;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-yellow text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600" style="color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;">{{ isset($stats['cotisations_mois']) ? number_format($stats['cotisations_mois']) . ' FCFA' : '0 FCFA' }}</h4>
                        <h6 class="text-white m-b-0" style="color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;">Cotisations ce mois</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-bar-chart f-28 text-white" style="color: #ffffff !important;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-red text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600" style="color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;">{{ isset($stats['cotisations_en_retard']) ? number_format($stats['cotisations_en_retard']) : '0' }}</h4>
                        <h6 class="text-white m-b-0" style="color: #ffffff !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;">Cotisations en retard</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-alert-triangle f-28 text-white" style="color: #ffffff !important;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-zap text-c-red me-2"></i>Actions rapides</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ route('adherents.create') }}" class="btn btn-primary btn-block">
                            <i class="feather icon-user-plus me-2"></i>Nouvel Adhérent
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('cotisations.create') }}" class="btn btn-success btn-block">
                            <i class="feather icon-plus me-2"></i>Nouvelle Cotisation
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('pensions.create') }}" class="btn btn-warning btn-block">
                            <i class="feather icon-file-text me-2"></i>Dossier Pension
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('outils.index') }}" class="btn btn-info btn-block">
                            <i class="feather icon-tool me-2"></i>Outils
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modules principaux -->
<div class="row">
    <div class="col-xl-4 col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-users text-c-blue me-2"></i> Adhérents</h5>
            </div>
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-c-blue f-w-600">{{ isset($stats['total_adherents']) ? number_format($stats['total_adherents']) : '0' }}</h4>
                        <h6 class="text-muted m-b-0">Total adhérents</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-users f-28 text-c-blue"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-c-blue">
                <div class="row align-items-center">
                    <div class="col-9">
                        <p class="text-white m-b-0">Gérer les adhérents</p>
                    </div>
                    <div class="col-3 text-end">
                        <a href="{{ route('adherents.index') }}" class="text-white"><i class="feather icon-chevrons-right m-l-5"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-credit-card text-c-green me-2"></i> Cotisations</h5>
            </div>
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-c-green f-w-600">{{ isset($stats['total_cotisations']) ? number_format($stats['total_cotisations']) : '0' }}</h4>
                        <h6 class="text-muted m-b-0">Total cotisations</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-credit-card f-28 text-c-green"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-c-green">
                <div class="row align-items-center">
                    <div class="col-9">
                        <p class="text-white m-b-0">Gérer les cotisations</p>
                    </div>
                    <div class="col-3 text-end">
                        <a href="{{ route('cotisations.index') }}" class="text-white"><i class="feather icon-chevrons-right m-l-5"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-briefcase text-c-yellow me-2"></i> Pensions</h5>
            </div>
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-c-yellow f-w-600">{{ isset($stats['pensions_en_cours']) ? number_format($stats['pensions_en_cours']) : '0' }}</h4>
                        <h6 class="text-muted m-b-0">Dossiers en cours</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-briefcase f-28 text-c-yellow"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-c-yellow">
                <div class="row align-items-center">
                    <div class="col-9">
                        <p class="text-white m-b-0">Gérer les pensions</p>
                    </div>
                    <div class="col-3 text-end">
                        <a href="{{ route('pensions.index') }}" class="text-white"><i class="feather icon-chevrons-right m-l-5"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Actualiser la date toutes les minutes
    setInterval(function() {
        const now = new Date();
        const dateStr = now.toLocaleDateString('fr-FR');
        document.getElementById('current-date').textContent = dateStr;
    }, 60000);
});
</script>
@endpush

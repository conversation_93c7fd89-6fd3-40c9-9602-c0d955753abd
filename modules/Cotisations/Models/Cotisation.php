<?php

namespace Modules\Cotisations\Models;

use App\Models\BaseModel;
use Modules\Adherents\Models\Adherent;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cotisation extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'adherent_id',
        'numero_cotisation',
        'periode_cotisation',
        'annee_cotisation',
        'mois_cotisation',
        'montant_base',
        'taux_cotisation',
        'montant_cotisation',
        'montant_employeur',
        'montant_employe',
        'date_echeance',
        'date_paiement',
        'mode_paiement',
        'reference_paiement',
        'statut',
        'observations',
        'created_by',
        'validated_by',
        'validated_at'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'montant_base' => 'decimal:2',
        'taux_cotisation' => 'decimal:4',
        'montant_cotisation' => 'decimal:2',
        'montant_employeur' => 'decimal:2',
        'montant_employe' => 'decimal:2',
        'date_echeance' => 'date',
        'date_paiement' => 'datetime',
        'validated_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Searchable fields
     */
    protected array $searchable = [
        'numero_cotisation',
        'periode_cotisation',
        'reference_paiement'
    ];

    /**
     * Status constants
     */
    const STATUS_EN_ATTENTE = 'en_attente';
    const STATUS_PAYEE = 'payee';
    const STATUS_EN_RETARD = 'en_retard';
    const STATUS_ANNULEE = 'annulee';

    /**
     * Payment mode constants
     */
    const MODE_ESPECES = 'especes';
    const MODE_CHEQUE = 'cheque';
    const MODE_VIREMENT = 'virement';
    const MODE_MOBILE_MONEY = 'mobile_money';
    const MODE_PRELEVEMENT = 'prelevement';

    /**
     * Get adherent that owns this cotisation
     */
    public function adherent(): BelongsTo
    {
        return $this->belongsTo(Adherent::class);
    }

    /**
     * Get user who created this cotisation
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get user who validated this cotisation
     */
    public function validator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'validated_by');
    }

    /**
     * Get echeanciers for this cotisation
     */
    public function echeanciers(): HasMany
    {
        return $this->hasMany(Echeancier::class);
    }

    /**
     * Get relances for this cotisation
     */
    public function relances(): HasMany
    {
        return $this->hasMany(Relance::class);
    }

    /**
     * Get formatted montant cotisation
     */
    public function getFormattedMontantAttribute(): string
    {
        return number_format($this->montant_cotisation, 0, ',', ' ') . ' €';
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_EN_ATTENTE => 'En attente',
            self::STATUS_PAYEE => 'Payée',
            self::STATUS_EN_RETARD => 'En retard',
            self::STATUS_ANNULEE => 'Annulée',
            default => 'Inconnu'
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_EN_ATTENTE => 'warning',
            self::STATUS_PAYEE => 'success',
            self::STATUS_EN_RETARD => 'danger',
            self::STATUS_ANNULEE => 'secondary',
            default => 'secondary'
        };
    }

    /**
     * Get payment mode label
     */
    public function getPaymentModeLabelAttribute(): string
    {
        return match($this->mode_paiement) {
            self::MODE_ESPECES => 'Espèces',
            self::MODE_CHEQUE => 'Chèque',
            self::MODE_VIREMENT => 'Virement bancaire',
            self::MODE_MOBILE_MONEY => 'Mobile Money',
            self::MODE_PRELEVEMENT => 'Prélèvement automatique',
            default => 'Non défini'
        };
    }

    /**
     * Check if cotisation is overdue
     */
    public function isOverdue(): bool
    {
        return $this->statut !== self::STATUS_PAYEE && 
               $this->date_echeance < now()->startOfDay();
    }

    /**
     * Check if cotisation is paid
     */
    public function isPaid(): bool
    {
        return $this->statut === self::STATUS_PAYEE;
    }

    /**
     * Scope for paid cotisations
     */
    public function scopePaid($query)
    {
        return $query->where('statut', self::STATUS_PAYEE);
    }

    /**
     * Scope for pending cotisations
     */
    public function scopePending($query)
    {
        return $query->where('statut', self::STATUS_EN_ATTENTE);
    }

    /**
     * Scope for overdue cotisations
     */
    public function scopeOverdue($query)
    {
        return $query->where('statut', '!=', self::STATUS_PAYEE)
                    ->where('date_echeance', '<', now()->startOfDay());
    }

    /**
     * Scope by year
     */
    public function scopeByYear($query, int $year)
    {
        return $query->where('annee_cotisation', $year);
    }

    /**
     * Scope by month
     */
    public function scopeByMonth($query, int $month)
    {
        return $query->where('mois_cotisation', $month);
    }

    /**
     * Scope by period
     */
    public function scopeByPeriod($query, string $period)
    {
        return $query->where('periode_cotisation', $period);
    }



    /**
     * Boot method for model events
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($cotisation) {
            if (empty($cotisation->uuid)) {
                $cotisation->uuid = (string) Str::uuid();
            }
            if (empty($cotisation->numero_cotisation)) {
                $cotisation->numero_cotisation = static::generateNextNumero();
            } else {
                // Vérifier si le numéro existe déjà
                $existing = static::where('numero_cotisation', $cotisation->numero_cotisation)
                    ->where('id', '!=', $cotisation->id ?? 0)
                    ->first();
                if ($existing) {
                    // Générer un nouveau numéro unique
                    $cotisation->numero_cotisation = static::generateNextNumero();
                }
            }
        });
    }

    /**
     * Calculate cotisation amounts based on base salary and rate
     */
    public function calculateAmounts(): void
    {
        $this->montant_cotisation = $this->montant_base * ($this->taux_cotisation / 100);
        
        // Assuming 50/50 split between employer and employee
        $this->montant_employeur = $this->montant_cotisation * 0.5;
        $this->montant_employe = $this->montant_cotisation * 0.5;
    }

    /**
     * Mark as paid
     */
    public function markAsPaid(string $paymentMode, string $reference = null): void
    {
        $this->update([
            'statut' => self::STATUS_PAYEE,
            'date_paiement' => now(),
            'mode_paiement' => $paymentMode,
            'reference_paiement' => $reference,
        ]);
    }

    /**
     * Generate next cotisation number
     */
    public static function generateNextNumero(): string
    {
        $year = date('Y');

        // Trouver le dernier numéro de cotisation pour l'année courante
        $lastCotisation = static::where('numero_cotisation', 'LIKE', "COT{$year}%")
                               ->orderBy('numero_cotisation', 'desc')
                               ->first();

        if ($lastCotisation) {
            // Extraire le numéro séquentiel (les 5 derniers chiffres)
            $lastNumber = (int) substr($lastCotisation->numero_cotisation, -5);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return "COT{$year}" . str_pad($nextNumber, 5, '0', STR_PAD_LEFT);
    }

    /**
     * Generate cotisation number for specific period
     */
    public static function generateNumeroForPeriod($year, $month): string
    {
        // Générer un numéro unique basé sur l'année et un compteur global
        $lastCotisation = static::where('numero_cotisation', 'LIKE', "COT{$year}%")
                               ->orderBy('numero_cotisation', 'desc')
                               ->first();

        if ($lastCotisation) {
            $lastNumber = (int) substr($lastCotisation->numero_cotisation, -5);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return "COT{$year}" . str_pad($nextNumber, 5, '0', STR_PAD_LEFT);
    }

    /**
     * Fix duplicate numero_cotisation by regenerating unique numbers
     */
    public static function fixDuplicateNumeros(): array
    {
        $results = [];

        // Trouver les doublons
        $doublons = static::select('numero_cotisation', \DB::raw('COUNT(*) as count'))
            ->groupBy('numero_cotisation')
            ->having('count', '>', 1)
            ->get();

        foreach ($doublons as $doublon) {
            $cotisations = static::where('numero_cotisation', $doublon->numero_cotisation)
                ->orderBy('created_at')
                ->get();

            // Garder la première, régénérer les numéros pour les autres
            $toKeep = $cotisations->first();
            $toUpdate = $cotisations->skip(1);

            $results[$doublon->numero_cotisation] = [
                'kept' => $toKeep->id,
                'updated' => []
            ];

            foreach ($toUpdate as $cotisation) {
                $oldNumero = $cotisation->numero_cotisation;
                $newNumero = static::generateNextNumero();

                $cotisation->update(['numero_cotisation' => $newNumero]);

                $results[$doublon->numero_cotisation]['updated'][] = [
                    'id' => $cotisation->id,
                    'old_numero' => $oldNumero,
                    'new_numero' => $newNumero
                ];
            }
        }

        return $results;
    }
}

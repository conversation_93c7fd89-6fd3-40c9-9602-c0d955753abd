<?php

namespace Modules\Auth\Controllers;

use App\Http\Controllers\BaseController;
use Modules\Auth\Services\AuthService;
use Modules\Auth\Requests\LoginRequest;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class AuthController extends BaseController
{
    protected AuthService $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Show login form
     */
    public function showLoginForm(): View
    {
        return view('auth::login-template');
    }

    /**
     * Handle login request
     */
    public function login(LoginRequest $request): RedirectResponse
    {
        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        if ($this->authService->login($credentials)) {
            $request->session()->regenerate();
            
            return $this->successRedirect('dashboard.index', 'Connexion réussie !');
        }

        return back()->withErrors([
            'email' => 'Les identifiants fournis ne correspondent pas à nos enregistrements.',
        ])->onlyInput('email');
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request): RedirectResponse
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('auth.login')->with('success', 'Déconnexion réussie !');
    }

    /**
     * Show forgot password form
     */
    public function showForgotPasswordForm(): View
    {
        return view('auth::forgot-password');
    }

    /**
     * Show user profile
     */
    public function showProfile(): View
    {
        $user = Auth::user();
        return view('auth::profile', compact('user'));
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . Auth::id(),
            'phone' => 'nullable|string|max:20',
            'current_password' => 'required_with:password',
            'password' => 'nullable|string|min:8|confirmed',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $user = Auth::user();

        // Vérifier le mot de passe actuel si un nouveau mot de passe est fourni
        if ($request->filled('password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                return $this->backWithError('Le mot de passe actuel est incorrect.');
            }
        }

        $data = $request->only('name', 'email', 'phone');

        // Gestion de l'avatar
        if ($request->hasFile('avatar')) {
            // Supprimer l'ancien avatar s'il existe
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $data['avatar'] = $avatarPath;
        }

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $user->update($data);

        return $this->backWithSuccess('Profil mis à jour avec succès !');
    }

    /**
     * Update user preferences
     */
    public function updatePreferences(Request $request): RedirectResponse
    {
        $request->validate([
            'email_notifications' => 'boolean',
            'language' => 'required|string|in:fr,en',
            'timezone' => 'required|string|max:50',
        ]);

        $user = Auth::user();

        $user->update([
            'email_notifications' => $request->has('email_notifications'),
            'language' => $request->language,
            'timezone' => $request->timezone,
        ]);

        return $this->backWithSuccess('Préférences mises à jour avec succès !');
    }
}

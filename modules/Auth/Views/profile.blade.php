@extends('layouts.template')

@section('title', '<PERSON> - CRFM')

@section('page-header')
@section('page-title', 'Mon Profil')
@section('page-description', 'Gestion de votre profil utilisateur')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Mon Profil</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Informations du profil -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-user text-c-blue me-2"></i>Informations personnelles</h5>
            </div>
            <div class="card-block">
                @if (session('success'))
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="feather icon-check-circle me-2"></i>{{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="feather icon-alert-circle me-2"></i>{{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="feather icon-alert-circle me-2"></i>
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <form method="POST" action="{{ route('auth.profile.update') }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-12">
                            <!-- Avatar -->
                            <div class="text-center mb-4">
                                <div class="user-avatar mb-3">
                                    @if($user->avatar)
                                        <img src="{{ asset('storage/' . $user->avatar) }}" alt="Avatar" 
                                             class="img-radius" style="width: 100px; height: 100px; object-fit: cover;">
                                    @else
                                        <div class="avatar bg-primary text-white d-inline-flex align-items-center justify-content-center" 
                                             style="width: 100px; height: 100px; font-size: 2.5rem; border-radius: 50%;">
                                            {{ strtoupper(substr($user->name, 0, 2)) }}
                                        </div>
                                    @endif
                                </div>
                                <div class="mb-3">
                                    <label for="avatar" class="form-label">Photo de profil</label>
                                    <input type="file" class="form-control {{ $errors->has('avatar') ? 'is-invalid' : '' }}"
                                           id="avatar" name="avatar" accept="image/*">
                                    @if($errors->has('avatar'))
                                        <div class="invalid-feedback">{{ $errors->first('avatar') }}</div>
                                    @endif
                                    <small class="form-text text-muted">Formats acceptés: JPG, PNG, GIF (max 2MB)</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nom complet <span class="text-danger">*</span></label>
                                <input type="text" class="form-control {{ $errors->has('name') ? 'is-invalid' : '' }}"
                                       id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                @if($errors->has('name'))
                                    <div class="invalid-feedback">{{ $errors->first('name') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Adresse email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control {{ $errors->has('email') ? 'is-invalid' : '' }}"
                                       id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                @if($errors->has('email'))
                                    <div class="invalid-feedback">{{ $errors->first('email') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control {{ $errors->has('phone') ? 'is-invalid' : '' }}"
                                       id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                                @if($errors->has('phone'))
                                    <div class="invalid-feedback">{{ $errors->first('phone') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">Rôle</label>
                                <input type="text" class="form-control" value="{{ ucfirst($user->role ?? 'Opérateur') }}" readonly>
                                <small class="form-text text-muted">Contactez un administrateur pour modifier votre rôle</small>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <h6 class="mb-3"><i class="feather icon-lock me-2"></i>Changer le mot de passe</h6>
                    <p class="text-muted mb-3">Laissez vide si vous ne souhaitez pas changer votre mot de passe</p>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Mot de passe actuel</label>
                                <input type="password" class="form-control {{ $errors->has('current_password') ? 'is-invalid' : '' }}"
                                       id="current_password" name="current_password">
                                @if($errors->has('current_password'))
                                    <div class="invalid-feedback">{{ $errors->first('current_password') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="password" class="form-label">Nouveau mot de passe</label>
                                <input type="password" class="form-control {{ $errors->has('password') ? 'is-invalid' : '' }}"
                                       id="password" name="password">
                                @if($errors->has('password'))
                                    <div class="invalid-feedback">{{ $errors->first('password') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">Confirmer le mot de passe</label>
                                <input type="password" class="form-control" 
                                       id="password_confirmation" name="password_confirmation">
                            </div>
                        </div>
                    </div>

                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="feather icon-save me-2"></i>Enregistrer les modifications
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Informations du compte -->
    <div class="col-md-4">
        <!-- Statistiques du compte -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-info text-c-green me-2"></i>Informations du compte</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-12">
                        <div class="info-item mb-3">
                            <label class="text-muted">Membre depuis</label>
                            <p class="mb-0">{{ $user->created_at->format('d/m/Y') }}</p>
                        </div>
                        <div class="info-item mb-3">
                            <label class="text-muted">Dernière connexion</label>
                            <p class="mb-0">{{ $user->last_login_at ? $user->last_login_at->format('d/m/Y H:i') : 'Jamais' }}</p>
                        </div>
                        <div class="info-item mb-3">
                            <label class="text-muted">Statut</label>
                            <p class="mb-0">
                                <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                    {{ $user->is_active ? 'Actif' : 'Inactif' }}
                                </span>
                            </p>
                        </div>
                        <div class="info-item mb-3">
                            <label class="text-muted">Rôles</label>
                            <div>
                                @forelse($user->roles as $role)
                                    <span class="badge bg-primary me-1">{{ $role->name }}</span>
                                @empty
                                    <span class="text-muted">Aucun rôle assigné</span>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Préférences -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-sliders text-c-purple me-2"></i>Préférences</h5>
            </div>
            <div class="card-block">
                <form method="POST" action="{{ route('auth.profile.preferences') }}">
                    @csrf
                    @method('PUT')
                    <div class="mb-3">
                        <label class="form-label">Notifications email</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="email_notifications" 
                                   name="email_notifications" {{ $user->email_notifications ? 'checked' : '' }}>
                            <label class="form-check-label" for="email_notifications">
                                Recevoir les notifications par email
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Langue</label>
                        <select class="form-select" name="language">
                            <option value="fr" {{ ($user->language ?? 'fr') === 'fr' ? 'selected' : '' }}>Français</option>
                            <option value="en" {{ ($user->language ?? 'fr') === 'en' ? 'selected' : '' }}>English</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Fuseau horaire</label>
                        <select class="form-select" name="timezone">
                            <option value="Africa/Bamako" {{ ($user->timezone ?? 'Africa/Bamako') === 'Africa/Bamako' ? 'selected' : '' }}>
                                Africa/Bamako (GMT+0)
                            </option>
                            <option value="Europe/Paris" {{ ($user->timezone ?? 'Africa/Bamako') === 'Europe/Paris' ? 'selected' : '' }}>
                                Europe/Paris (GMT+1)
                            </option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-sm btn-outline-primary">
                        <i class="feather icon-save me-1"></i>Sauvegarder
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Prévisualisation de l'avatar
document.getElementById('avatar').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const avatar = document.querySelector('.user-avatar img, .user-avatar .avatar');
            if (avatar.tagName === 'IMG') {
                avatar.src = e.target.result;
            } else {
                // Remplacer le div avatar par une image
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'img-radius';
                img.style.cssText = 'width: 100px; height: 100px; object-fit: cover;';
                avatar.parentNode.replaceChild(img, avatar);
            }
        };
        reader.readAsDataURL(file);
    }
});

// Validation du formulaire
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const currentPassword = document.getElementById('current_password').value;
    
    if (password && !currentPassword) {
        e.preventDefault();
        alert('Veuillez saisir votre mot de passe actuel pour changer votre mot de passe.');
        document.getElementById('current_password').focus();
    }
});
</script>
@endpush

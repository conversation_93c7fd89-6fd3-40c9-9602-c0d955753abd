<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use Modules\Adherents\Models\Adherent;
use Modules\Cotisations\Models\Cotisation;

class SystemTest extends BaseTestCase
{
    use CreatesApplication;

    /**
     * Test de la page d'accueil (redirection vers login)
     */
    public function test_homepage_redirects_to_login()
    {
        $response = $this->get('/');
        $response->assertStatus(302);
        $response->assertRedirect('/login');
    }

    /**
     * Test de la page de connexion
     */
    public function test_login_page_loads()
    {
        $response = $this->get('/login');
        $response->assertStatus(200);
    }

    /**
     * Test de l'authentification
     */
    public function test_user_can_login()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123')
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $this->assertAuthenticatedAs($user);
    }

    /**
     * Test de la page des adhérents
     */
    public function test_adherents_page_loads()
    {
        $user = User::first();
        $response = $this->actingAs($user)->get('/adherents');
        $response->assertStatus(200);
    }

    /**
     * Test de la page des cotisations
     */
    public function test_cotisations_page_loads()
    {
        $user = User::first();
        $response = $this->actingAs($user)->get('/cotisations');
        $response->assertStatus(200);
    }

    /**
     * Test de la page des pensions
     */
    public function test_pensions_page_loads()
    {
        $user = User::first();
        $response = $this->actingAs($user)->get('/pensions');
        $response->assertStatus(200);
    }

    /**
     * Test de la page des paramètres
     */
    public function test_settings_page_loads()
    {
        $user = User::first();
        $response = $this->actingAs($user)->get('/parametres');
        $response->assertStatus(200);
    }

    /**
     * Test de la page du profil
     */
    public function test_profile_page_loads()
    {
        $user = User::first();
        $response = $this->actingAs($user)->get('/profile');
        $response->assertStatus(200);
    }

    /**
     * Test des modèles
     */
    public function test_models_work()
    {
        // Test User
        $userCount = User::count();
        $this->assertGreaterThanOrEqual(1, $userCount);

        // Test Adherent
        $adherentCount = Adherent::count();
        $this->assertGreaterThanOrEqual(0, $adherentCount);

        // Test Cotisation
        $cotisationCount = Cotisation::count();
        $this->assertGreaterThanOrEqual(0, $cotisationCount);
    }

    /**
     * Test de la base de données
     */
    public function test_database_connection()
    {
        $this->assertTrue(\DB::connection()->getPdo() !== null);
    }

    /**
     * Test des services
     */
    public function test_services_work()
    {
        $outilsService = new \App\Services\OutilsService();
        $stats = $outilsService->getStatistiquesVerification();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('adherents_valides', $stats);
        $this->assertArrayHasKey('cotisations_coherentes', $stats);
    }
}

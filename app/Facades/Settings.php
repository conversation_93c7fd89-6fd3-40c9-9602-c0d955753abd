<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static mixed get(string $key, $default = null)
 * @method static void set(string $key, $value, string $type = 'string', string $group = 'general', string $label = null, string $description = null)
 * @method static array all()
 * @method static string appName()
 * @method static string organizationName()
 * @method static string currency()
 * @method static string dateFormat()
 * @method static bool isMaintenanceMode()
 * @method static bool isRegistrationEnabled()
 * @method static int itemsPerPage()
 * @method static int sessionTimeout()
 * @method static string formatCurrency(float $amount)
 * @method static string formatDate($date)
 */
class Settings extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'settings';
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestAllPages extends Command
{
    protected $signature = 'test:pages';
    protected $description = 'Test toutes les pages de l\'application CRFM';

    private $baseUrl = 'http://127.0.0.1:8000';
    private $pages = [
        'Dashboard' => '/',
        'Adhérents' => '/adherents',
        'Cotisations' => '/cotisations',
        'Pensions' => '/pensions',
        'Outils' => '/outils',
        'Paramètres' => '/parametres',
        'Utilisateurs' => '/users',
        'Sécurité' => '/parametres/securite',
        'Notifications' => '/parametres/notifications',
        'Profil' => '/profile',
        'Login' => '/login',
    ];

    public function handle()
    {
        $this->info('🧪 Test de toutes les pages CRFM...');
        $this->newLine();

        $results = [];
        $totalPages = count($this->pages);
        $successCount = 0;

        foreach ($this->pages as $name => $url) {
            $this->line("Testing {$name}...");
            
            try {
                $response = Http::timeout(10)->get($this->baseUrl . $url);
                $status = $response->status();
                
                if ($status === 200) {
                    $results[$name] = ['status' => 'OK', 'code' => $status];
                    $successCount++;
                    $this->info("  ✅ {$name}: OK ({$status})");
                } elseif ($status === 302) {
                    $results[$name] = ['status' => 'REDIRECT', 'code' => $status];
                    $this->warn("  🔄 {$name}: Redirection ({$status})");
                } else {
                    $results[$name] = ['status' => 'ERROR', 'code' => $status];
                    $this->error("  ❌ {$name}: Erreur ({$status})");
                }
            } catch (\Exception $e) {
                $results[$name] = ['status' => 'EXCEPTION', 'code' => 'N/A'];
                $this->error("  ❌ {$name}: Exception - " . $e->getMessage());
            }
        }

        $this->newLine();
        $this->displaySummary($results, $successCount, $totalPages);
        
        return $successCount >= ($totalPages - 2) ? 0 : 1; // Tolérer 2 redirections
    }

    private function displaySummary(array $results, int $successCount, int $totalPages): void
    {
        $this->info('📊 Résumé des tests:');
        $this->newLine();

        $this->table(
            ['Page', 'Statut', 'Code'],
            collect($results)->map(function ($result, $page) {
                $icon = match($result['status']) {
                    'OK' => '✅',
                    'REDIRECT' => '🔄',
                    'ERROR' => '❌',
                    'EXCEPTION' => '💥',
                    default => '❓'
                };
                
                return [
                    $page,
                    $icon . ' ' . $result['status'],
                    $result['code']
                ];
            })->values()->toArray()
        );

        $this->newLine();
        
        $redirectCount = collect($results)->where('status', 'REDIRECT')->count();
        $okCount = collect($results)->where('status', 'OK')->count();
        $totalWorking = $okCount + $redirectCount;
        
        if ($totalWorking >= ($totalPages - 1)) {
            $this->info("🎉 Système opérationnel ! ({$okCount} OK, {$redirectCount} redirections)");
        } else {
            $errorCount = $totalPages - $totalWorking;
            $this->warn("⚠️  {$totalWorking}/{$totalPages} pages fonctionnelles, {$errorCount} erreurs");
        }

        $this->newLine();
        $this->line('💡 Note: Les redirections (302) sont normales pour les pages protégées');
    }
}

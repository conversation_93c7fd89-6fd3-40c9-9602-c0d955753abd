<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SystemMaintenance extends Command
{
    protected $signature = 'system:maintenance {--optimize : Optimiser le système}';
    protected $description = 'Maintenance et optimisation du système CRFM';

    public function handle()
    {
        $this->info('🔧 Maintenance du système CRFM...');
        $this->newLine();

        if ($this->option('optimize')) {
            $this->optimizeSystem();
        } else {
            $this->basicMaintenance();
        }

        $this->info('✅ Maintenance terminée !');
    }

    private function basicMaintenance()
    {
        $this->info('📋 Maintenance de base...');

        // Vider le cache
        $this->info('→ Vidage du cache...');
        Cache::flush();
        $this->line('  ✅ Cache vidé');

        // Vider les logs anciens
        $this->info('→ Nettoyage des logs...');
        $logPath = storage_path('logs');
        $files = glob($logPath . '/laravel-*.log');
        $deleted = 0;

        foreach ($files as $file) {
            if (filemtime($file) < strtotime('-7 days')) {
                unlink($file);
                $deleted++;
            }
        }

        $this->line("  ✅ {$deleted} fichiers de log supprimés");

        // Optimiser la base de données
        $this->info('→ Optimisation de la base de données...');
        try {
            DB::statement('OPTIMIZE TABLE users, adherents, cotisations, dossier_pensions');
            $this->line('  ✅ Base de données optimisée');
        } catch (\Exception $e) {
            $this->line("  ❌ Erreur: " . $e->getMessage());
        }
    }

    private function optimizeSystem()
    {
        $this->info('⚡ Optimisation complète du système...');

        // Maintenance de base
        $this->basicMaintenance();

        // Optimisation des vues
        $this->info('→ Optimisation des vues...');
        Artisan::call('view:cache');
        $this->line('  ✅ Vues mises en cache');

        // Optimisation des routes
        $this->info('→ Optimisation des routes...');
        Artisan::call('route:cache');
        $this->line('  ✅ Routes mises en cache');

        // Optimisation de la configuration
        $this->info('→ Optimisation de la configuration...');
        Artisan::call('config:cache');
        $this->line('  ✅ Configuration mise en cache');

        // Optimisation de l'autoloader
        $this->info('→ Optimisation de l\'autoloader...');
        Artisan::call('optimize');
        $this->line('  ✅ Autoloader optimisé');

        // Statistiques finales
        $this->newLine();
        $this->info('📊 Statistiques du système:');
        
        $stats = [
            'Utilisateurs' => DB::table('users')->count(),
            'Adhérents' => DB::table('adherents')->count(),
            'Cotisations' => DB::table('cotisations')->count(),
            'Dossiers de pension' => DB::table('dossier_pensions')->count(),
        ];

        foreach ($stats as $label => $count) {
            $this->line("  → {$label}: {$count}");
        }
    }
}

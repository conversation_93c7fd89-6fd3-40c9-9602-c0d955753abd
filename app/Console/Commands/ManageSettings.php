<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ManageSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settings:manage
                            {action : Action to perform (list, get, set, delete)}
                            {key? : Setting key}
                            {value? : Setting value}
                            {--type=string : Value type (string, boolean, integer, json)}
                            {--group=general : Setting group}
                            {--label= : Setting label}
                            {--description= : Setting description}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage system settings from command line';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'list':
                $this->listSettings();
                break;
            case 'get':
                $this->getSetting();
                break;
            case 'set':
                $this->setSetting();
                break;
            case 'delete':
                $this->deleteSetting();
                break;
            default:
                $this->error("Action invalide. Actions disponibles: list, get, set, delete");
                return 1;
        }

        return 0;
    }

    /**
     * List all settings
     */
    protected function listSettings(): void
    {
        $settings = \App\Models\SystemSetting::all()->groupBy('group');

        if ($settings->isEmpty()) {
            $this->info('Aucun paramètre trouvé.');
            return;
        }

        foreach ($settings as $group => $groupSettings) {
            $this->line("📁 <fg=yellow>{$group}</>");

            $tableData = [];
            foreach ($groupSettings as $setting) {
                $value = $setting->value;
                if ($setting->type === 'boolean') {
                    $value = $value ? '✅ true' : '❌ false';
                } elseif (strlen($value) > 50) {
                    $value = substr($value, 0, 47) . '...';
                }

                $tableData[] = [
                    $setting->key,
                    $value,
                    $setting->type,
                    $setting->label
                ];
            }

            $this->table(['Clé', 'Valeur', 'Type', 'Label'], $tableData);
            $this->line('');
        }
    }

    /**
     * Get a specific setting
     */
    protected function getSetting(): void
    {
        $key = $this->argument('key');

        if (!$key) {
            $this->error('Veuillez spécifier une clé de paramètre.');
            return;
        }

        $setting = \App\Models\SystemSetting::where('key', $key)->first();

        if (!$setting) {
            $this->error("Paramètre '{$key}' non trouvé.");
            return;
        }

        $this->info("📋 Paramètre: {$setting->key}");
        $this->line("Valeur: {$setting->value}");
        $this->line("Type: {$setting->type}");
        $this->line("Groupe: {$setting->group}");
        $this->line("Label: {$setting->label}");
        if ($setting->description) {
            $this->line("Description: {$setting->description}");
        }
    }

    /**
     * Set a setting value
     */
    protected function setSetting(): void
    {
        $key = $this->argument('key');
        $value = $this->argument('value');

        if (!$key || $value === null) {
            $this->error('Veuillez spécifier une clé et une valeur.');
            return;
        }

        $type = $this->option('type');
        $group = $this->option('group');
        $label = $this->option('label') ?: $key;
        $description = $this->option('description');

        \App\Models\SystemSetting::setValue($key, $value, $type, $group, $label, $description);

        $this->info("✅ Paramètre '{$key}' défini avec succès.");
        $this->line("Valeur: {$value}");
        $this->line("Type: {$type}");
        $this->line("Groupe: {$group}");
    }

    /**
     * Delete a setting
     */
    protected function deleteSetting(): void
    {
        $key = $this->argument('key');

        if (!$key) {
            $this->error('Veuillez spécifier une clé de paramètre.');
            return;
        }

        $setting = \App\Models\SystemSetting::where('key', $key)->first();

        if (!$setting) {
            $this->error("Paramètre '{$key}' non trouvé.");
            return;
        }

        if ($this->confirm("Êtes-vous sûr de vouloir supprimer le paramètre '{$key}' ?")) {
            $setting->delete();
            \Illuminate\Support\Facades\Cache::forget("setting.{$key}");
            $this->info("✅ Paramètre '{$key}' supprimé avec succès.");
        } else {
            $this->info('Suppression annulée.');
        }
    }
}

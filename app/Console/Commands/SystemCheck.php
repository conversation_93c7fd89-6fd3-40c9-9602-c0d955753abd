<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use Modules\Adherents\Models\Adherent;
use Modules\Cotisations\Models\Cotisation;

class SystemCheck extends Command
{
    protected $signature = 'system:check';
    protected $description = 'Vérification complète du système CRFM';

    public function handle()
    {
        $this->info('🔍 Vérification du système CRFM...');
        $this->newLine();

        $checks = [
            'database' => $this->checkDatabase(),
            'models' => $this->checkModels(),
            'storage' => $this->checkStorage(),
            'cache' => $this->checkCache(),
            'permissions' => $this->checkPermissions(),
            'config' => $this->checkConfig(),
        ];

        $this->displayResults($checks);
        
        $allPassed = collect($checks)->every(fn($check) => $check['status'] === 'OK');
        
        if ($allPassed) {
            $this->info('✅ Tous les tests sont passés ! Le système est opérationnel.');
        } else {
            $this->error('❌ Certains tests ont échoué. Vérifiez les détails ci-dessus.');
        }

        return $allPassed ? 0 : 1;
    }

    private function checkDatabase(): array
    {
        try {
            DB::connection()->getPdo();
            $dbName = DB::connection()->getDatabaseName();
            
            return [
                'status' => 'OK',
                'message' => "Connexion réussie à la base '{$dbName}'"
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'ERREUR',
                'message' => 'Impossible de se connecter à la base de données: ' . $e->getMessage()
            ];
        }
    }

    private function checkModels(): array
    {
        try {
            $users = User::count();
            $adherents = Adherent::count();
            $cotisations = Cotisation::count();
            
            return [
                'status' => 'OK',
                'message' => "Modèles fonctionnels - Users: {$users}, Adhérents: {$adherents}, Cotisations: {$cotisations}"
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'ERREUR',
                'message' => 'Erreur avec les modèles: ' . $e->getMessage()
            ];
        }
    }

    private function checkStorage(): array
    {
        try {
            $storagePath = storage_path();
            $publicPath = storage_path('app/public');
            
            $storageWritable = is_writable($storagePath);
            $publicExists = is_dir($publicPath);
            $linkExists = is_link(public_path('storage'));
            
            if (!$storageWritable) {
                return [
                    'status' => 'ERREUR',
                    'message' => 'Le dossier storage n\'est pas accessible en écriture'
                ];
            }
            
            if (!$linkExists) {
                return [
                    'status' => 'ATTENTION',
                    'message' => 'Le lien symbolique storage n\'existe pas. Exécutez: php artisan storage:link'
                ];
            }
            
            return [
                'status' => 'OK',
                'message' => 'Storage accessible et lien symbolique présent'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'ERREUR',
                'message' => 'Erreur storage: ' . $e->getMessage()
            ];
        }
    }

    private function checkCache(): array
    {
        try {
            $testKey = 'system_check_' . time();
            $testValue = 'test_value';
            
            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);
            
            if ($retrieved === $testValue) {
                return [
                    'status' => 'OK',
                    'message' => 'Cache fonctionnel'
                ];
            } else {
                return [
                    'status' => 'ERREUR',
                    'message' => 'Le cache ne fonctionne pas correctement'
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'ERREUR',
                'message' => 'Erreur cache: ' . $e->getMessage()
            ];
        }
    }

    private function checkPermissions(): array
    {
        $paths = [
            storage_path(),
            storage_path('logs'),
            storage_path('framework'),
            base_path('bootstrap/cache'),
        ];

        $issues = [];
        foreach ($paths as $path) {
            if (!is_writable($path)) {
                $issues[] = $path;
            }
        }

        if (empty($issues)) {
            return [
                'status' => 'OK',
                'message' => 'Toutes les permissions sont correctes'
            ];
        } else {
            return [
                'status' => 'ERREUR',
                'message' => 'Permissions incorrectes pour: ' . implode(', ', $issues)
            ];
        }
    }

    private function checkConfig(): array
    {
        $issues = [];

        if (!config('app.key')) {
            $issues[] = 'APP_KEY manquante';
        }

        if (config('app.debug') && config('app.env') === 'production') {
            $issues[] = 'DEBUG activé en production';
        }

        if (!config('database.connections.mysql.database')) {
            $issues[] = 'Base de données non configurée';
        }

        if (empty($issues)) {
            return [
                'status' => 'OK',
                'message' => 'Configuration correcte'
            ];
        } else {
            return [
                'status' => 'ATTENTION',
                'message' => 'Problèmes de configuration: ' . implode(', ', $issues)
            ];
        }
    }

    private function displayResults(array $checks): void
    {
        $this->table(
            ['Composant', 'Statut', 'Message'],
            collect($checks)->map(function ($check, $component) {
                $status = $check['status'];
                $icon = match($status) {
                    'OK' => '✅',
                    'ATTENTION' => '⚠️',
                    'ERREUR' => '❌',
                    default => '❓'
                };
                
                return [
                    ucfirst($component),
                    $icon . ' ' . $status,
                    $check['message']
                ];
            })->values()->toArray()
        );
    }
}

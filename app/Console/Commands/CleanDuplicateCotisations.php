<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CleanDuplicateCotisations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cotisations:clean-duplicates {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean duplicate cotisations and fix numero_cotisation conflicts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Recherche et correction des cotisations en double...');

        if ($this->option('dry-run')) {
            // Mode dry-run : juste afficher ce qui serait fait
            $doublons = \Modules\Cotisations\Models\Cotisation::select('numero_cotisation', \DB::raw('COUNT(*) as count'))
                ->groupBy('numero_cotisation')
                ->having('count', '>', 1)
                ->get();

            if ($doublons->isEmpty()) {
                $this->info('✅ Aucun doublon trouvé.');
                return;
            }

            $this->warn("⚠️  {$doublons->count()} numéros en double trouvés :");

            foreach ($doublons as $doublon) {
                $cotisations = \Modules\Cotisations\Models\Cotisation::where('numero_cotisation', $doublon->numero_cotisation)
                    ->orderBy('created_at')
                    ->get();

                $this->line("📋 Numéro: {$doublon->numero_cotisation} ({$doublon->count} occurrences)");

                foreach ($cotisations as $index => $cotisation) {
                    $action = $index === 0 ? '✅ GARDER' : '🔄 RÉGÉNÉRER NUMÉRO';
                    $this->line("   {$action} ID: {$cotisation->id} (créé le {$cotisation->created_at})");
                }
            }

            $this->info('🔍 Mode dry-run activé. Aucune modification effectuée.');
            $this->info('💡 Exécutez sans --dry-run pour effectuer les corrections.');

        } else {
            // Mode réel : corriger les doublons
            $results = \Modules\Cotisations\Models\Cotisation::fixDuplicateNumeros();

            if (empty($results)) {
                $this->info('✅ Aucun doublon trouvé.');
                return;
            }

            $this->info('✅ Correction des doublons terminée :');

            foreach ($results as $originalNumero => $result) {
                $this->line("📋 Numéro original: {$originalNumero}");
                $this->line("   ✅ Conservé ID: {$result['kept']}");

                foreach ($result['updated'] as $update) {
                    $this->line("   🔄 ID {$update['id']}: {$update['old_numero']} → {$update['new_numero']}");
                }
            }
        }

        // Afficher un résumé final
        $total = \Modules\Cotisations\Models\Cotisation::count();
        $this->info("📊 Total des cotisations: {$total}");

        // Vérifier qu'il n'y a plus de doublons
        $remainingDuplicates = \Modules\Cotisations\Models\Cotisation::select('numero_cotisation', \DB::raw('COUNT(*) as count'))
            ->groupBy('numero_cotisation')
            ->having('count', '>', 1)
            ->count();

        if ($remainingDuplicates === 0) {
            $this->info('✅ Aucun doublon restant.');
        } else {
            $this->error("❌ {$remainingDuplicates} doublons restants !");
        }
    }
}

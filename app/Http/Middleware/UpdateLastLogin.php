<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UpdateLastLogin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check()) {
            // Mettre à jour la dernière connexion seulement une fois par session
            $sessionKey = 'last_login_updated_' . auth()->id();

            if (!session()->has($sessionKey)) {
                auth()->user()->update(['last_login_at' => now()]);
                session()->put($sessionKey, true);
            }
        }

        return $next($request);
    }
}

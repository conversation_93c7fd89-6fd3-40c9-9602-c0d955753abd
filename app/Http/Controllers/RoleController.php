<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class RoleController extends Controller
{
    /**
     * Display a listing of roles and permissions.
     */
    public function index(): View
    {
        $roles = $this->getRoles();
        $permissions = $this->getPermissions();
        
        return view('roles.index', compact('roles', 'permissions'));
    }

    /**
     * Update role permissions.
     */
    public function updatePermissions(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'role' => ['required', 'in:admin,gestionnaire,operateur'],
            'permissions' => ['array'],
            'permissions.*' => ['string']
        ]);

        try {
            // Simuler la sauvegarde des permissions
            // Dans un vrai système, vous sauvegarderiez dans la base de données
            $role = $validated['role'];
            $permissions = $validated['permissions'] ?? [];

            // Log pour debug
            \Log::info('Permissions mises à jour', [
                'role' => $role,
                'permissions' => $permissions,
                'count' => count($permissions)
            ]);

            // Simuler un délai de traitement
            sleep(1);

            return redirect()->route('roles.index')
                ->with('success', "Permissions mises à jour avec succès pour le rôle {$role}. " . count($permissions) . " permission(s) assignée(s).");
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la mise à jour des permissions', [
                'error' => $e->getMessage(),
                'role' => $request->role ?? 'unknown'
            ]);

            return redirect()->back()
                ->with('error', 'Erreur lors de la mise à jour des permissions : ' . $e->getMessage());
        }
    }

    /**
     * Get all available roles with their descriptions.
     */
    private function getRoles(): array
    {
        return [
            'admin' => [
                'name' => 'Administrateur',
                'description' => 'Accès complet au système',
                'color' => 'danger',
                'users_count' => \App\Models\User::where('role', 'admin')->count(),
                'permissions' => [
                    'users.manage',
                    'settings.manage',
                    'system.manage',
                    'adherents.manage',
                    'cotisations.manage',
                    'pensions.manage',
                    'reports.advanced',
                    'backup.manage'
                ]
            ],
            'gestionnaire' => [
                'name' => 'Gestionnaire',
                'description' => 'Gestion des adhérents et cotisations',
                'color' => 'warning',
                'users_count' => \App\Models\User::where('role', 'gestionnaire')->count(),
                'permissions' => [
                    'adherents.manage',
                    'cotisations.manage',
                    'pensions.manage',
                    'reports.standard',
                    'statistics.view'
                ]
            ],
            'operateur' => [
                'name' => 'Opérateur',
                'description' => 'Consultation et saisie limitée',
                'color' => 'info',
                'users_count' => \App\Models\User::where('role', 'operateur')->count(),
                'permissions' => [
                    'adherents.view',
                    'adherents.create',
                    'cotisations.view',
                    'cotisations.create',
                    'pensions.view',
                    'reports.basic'
                ]
            ]
        ];
    }

    /**
     * Get all available permissions with their descriptions.
     */
    private function getPermissions(): array
    {
        return [
            'users.manage' => [
                'name' => 'Gestion des utilisateurs',
                'description' => 'Créer, modifier et supprimer des utilisateurs',
                'category' => 'Administration'
            ],
            'settings.manage' => [
                'name' => 'Gestion des paramètres',
                'description' => 'Modifier les paramètres système',
                'category' => 'Administration'
            ],
            'system.manage' => [
                'name' => 'Administration système',
                'description' => 'Accès aux outils d\'administration',
                'category' => 'Administration'
            ],
            'adherents.manage' => [
                'name' => 'Gestion complète des adhérents',
                'description' => 'Toutes les actions sur les adhérents',
                'category' => 'Adhérents'
            ],
            'adherents.view' => [
                'name' => 'Consultation des adhérents',
                'description' => 'Voir les informations des adhérents',
                'category' => 'Adhérents'
            ],
            'adherents.create' => [
                'name' => 'Création d\'adhérents',
                'description' => 'Créer de nouveaux adhérents',
                'category' => 'Adhérents'
            ],
            'cotisations.manage' => [
                'name' => 'Gestion complète des cotisations',
                'description' => 'Toutes les actions sur les cotisations',
                'category' => 'Cotisations'
            ],
            'cotisations.view' => [
                'name' => 'Consultation des cotisations',
                'description' => 'Voir les cotisations',
                'category' => 'Cotisations'
            ],
            'cotisations.create' => [
                'name' => 'Saisie de cotisations',
                'description' => 'Enregistrer de nouvelles cotisations',
                'category' => 'Cotisations'
            ],
            'pensions.manage' => [
                'name' => 'Gestion complète des pensions',
                'description' => 'Toutes les actions sur les pensions',
                'category' => 'Pensions'
            ],
            'pensions.view' => [
                'name' => 'Consultation des pensions',
                'description' => 'Voir les informations de pension',
                'category' => 'Pensions'
            ],
            'reports.advanced' => [
                'name' => 'Rapports avancés',
                'description' => 'Accès à tous les rapports',
                'category' => 'Rapports'
            ],
            'reports.standard' => [
                'name' => 'Rapports standards',
                'description' => 'Rapports de gestion courante',
                'category' => 'Rapports'
            ],
            'reports.basic' => [
                'name' => 'Rapports de base',
                'description' => 'Rapports simples et listes',
                'category' => 'Rapports'
            ],
            'statistics.view' => [
                'name' => 'Consultation des statistiques',
                'description' => 'Voir les tableaux de bord',
                'category' => 'Statistiques'
            ],
            'backup.manage' => [
                'name' => 'Gestion des sauvegardes',
                'description' => 'Créer et restaurer des sauvegardes',
                'category' => 'Administration'
            ]
        ];
    }

    /**
     * Check if a user has a specific permission.
     */
    public static function hasPermission($user, $permission): bool
    {
        $controller = new self();
        $roles = $controller->getRoles();
        
        if (!isset($roles[$user->role])) {
            return false;
        }
        
        return in_array($permission, $roles[$user->role]['permissions']);
    }

    /**
     * Get permissions for a specific role.
     */
    public static function getRolePermissions($role): array
    {
        $controller = new self();
        $roles = $controller->getRoles();
        
        return $roles[$role]['permissions'] ?? [];
    }
}

<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Enregistrer le helper Settings comme singleton
        $this->app->singleton('settings', function () {
            return new \App\Helpers\SettingsHelper();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Partager les paramètres avec toutes les vues
        view()->composer('*', function ($view) {
            $view->with('appSettings', \App\Helpers\SettingsHelper::all());
        });
    }
}

<?php

namespace App\Services;

use Modules\Adherents\Models\Adherent;
use Modules\Cotisations\Models\Cotisation;
use Modules\PreLiquidations\Models\PreLiquidation;
use Modules\Declarations\Models\Declaration;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\UploadedFile;
use Carbon\Carbon;

class OutilsService
{
    /**
     * Calculate pension with real CRFM rules
     */
    public function calculerPension(float $salaireReference, float $dureeCotisation, string $typePension, ?float $ageDepart = null): array
    {
        // Règles de calcul CRFM
        $tauxBase = min($dureeCotisation * 2, 75); // 2% par année, max 75%
        $tauxFinal = $tauxBase;
        
        // Ajustements selon le type de pension
        switch ($typePension) {
            case 'anticipee':
                $tauxFinal *= 0.95; // Réduction de 5%
                $coefficientMinoration = 0.95;
                break;
            case 'invalidite':
                $tauxFinal = max($tauxFinal, 50); // Minimum 50%
                $coefficientMinoration = 1.0;
                break;
            case 'survivant':
                $tauxFinal *= 0.6; // 60% de la pension
                $coefficientMinoration = 0.6;
                break;
            default: // vieillesse
                $coefficientMinoration = 1.0;
                break;
        }

        // Calculs
        $montantBrut = $salaireReference * ($tauxFinal / 100);
        $montantNet = $montantBrut * 0.8; // Estimation après charges
        
        // Informations complémentaires
        $ageRetraiteNormal = 60;
        $dureeRequise = 37.5; // années
        $trimestresRequis = $dureeRequise * 4;
        $trimestresActuels = $dureeCotisation * 4;

        return [
            'montant_brut' => round($montantBrut, 2),
            'montant_net' => round($montantNet, 2),
            'taux_liquidation' => round($tauxFinal, 2),
            'coefficient_minoration' => $coefficientMinoration,
            'duree_cotisation_annees' => $dureeCotisation,
            'trimestres_actuels' => $trimestresActuels,
            'trimestres_requis' => $trimestresRequis,
            'age_retraite_normal' => $ageRetraiteNormal,
            'type_pension' => $typePension,
            'details' => $this->getDetailsCalcul($typePension, $dureeCotisation, $tauxFinal)
        ];
    }

    /**
     * Generate unique number for different types
     */
    public function genererNumero(string $type): string
    {
        $annee = date('Y');
        
        switch ($type) {
            case 'affiliation':
                $prefix = 'AFF';
                $dernierNumero = Adherent::where('numero_adherent', 'like', $prefix . $annee . '%')
                                       ->orderBy('numero_adherent', 'desc')
                                       ->first();
                break;
            case 'declaration':
                $prefix = 'DECL';
                $dernierNumero = Declaration::where('numero_declaration', 'like', $prefix . $annee . '%')
                                           ->orderBy('numero_declaration', 'desc')
                                           ->first();
                break;
            case 'preliquidation':
                $prefix = 'PL';
                $dernierNumero = PreLiquidation::where('numero_dossier', 'like', $prefix . $annee . '%')
                                              ->orderBy('numero_dossier', 'desc')
                                              ->first();
                break;
            case 'cotisation':
                $prefix = 'COT';
                $dernierNumero = Cotisation::where('numero_cotisation', 'like', $prefix . $annee . '%')
                                         ->orderBy('numero_cotisation', 'desc')
                                         ->first();
                break;
            default:
                $prefix = 'ADH';
                $dernierNumero = Adherent::where('numero_adherent', 'like', $prefix . $annee . '%')
                                       ->orderBy('numero_adherent', 'desc')
                                       ->first();
                break;
        }

        // Extraire le dernier numéro et incrémenter
        $prochainNumero = 1;
        if ($dernierNumero) {
            $numeroField = match($type) {
                'affiliation', 'adherent' => 'numero_adherent',
                'declaration' => 'numero_declaration',
                'preliquidation' => 'numero_dossier',
                'cotisation' => 'numero_cotisation',
                default => 'numero_adherent'
            };
            
            $dernierNumeroStr = $dernierNumero->$numeroField;
            $dernierSequence = (int) substr($dernierNumeroStr, -4);
            $prochainNumero = $dernierSequence + 1;
        }

        return $prefix . $annee . str_pad($prochainNumero, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get verification statistics
     */
    public function getStatistiquesVerification(): array
    {
        return Cache::remember('outils.verification', 300, function () {
            return [
                'adherents_valides' => $this->compterAdherentsValides(),
                'cotisations_coherentes' => $this->compterCotisationsCoherentes(),
                'dossiers_complets' => $this->compterDossiersComplets(),
                'declarations_conformes' => $this->compterDeclarationsConformes(),
                'derniere_verification' => Carbon::now()->format('d/m/Y à H:i'),
                'statut_global' => $this->getStatutGlobalVerification()
            ];
        });
    }

    /**
     * Get recent tasks
     */
    public function getTachesRecentes(): array
    {
        return [
            [
                'titre' => 'Vérification mensuelle des cotisations',
                'priorite' => 'Haute',
                'statut' => 'En cours',
                'echeance' => Carbon::now()->addDays(2)->format('d/m/Y')
            ],
            [
                'titre' => 'Export des données trimestrielles',
                'priorite' => 'Moyenne',
                'statut' => 'Planifié',
                'echeance' => Carbon::now()->addDays(7)->format('d/m/Y')
            ],
            [
                'titre' => 'Rapport trimestriel Q1',
                'priorite' => 'Basse',
                'statut' => 'À venir',
                'echeance' => Carbon::now()->addDays(30)->format('d/m/Y')
            ]
        ];
    }

    /**
     * Verify data integrity
     */
    public function verifierIntegriteDonnees(): array
    {
        $resultats = [];

        // Vérification des adhérents
        $adherentsSansNumero = Adherent::whereNull('numero_adherent')->count();
        $adherentsDoublons = $this->detecterDoublonsAdherents();
        
        $resultats['adherents'] = [
            'total' => Adherent::count(),
            'sans_numero' => $adherentsSansNumero,
            'doublons' => $adherentsDoublons,
            'statut' => ($adherentsSansNumero === 0 && $adherentsDoublons === 0) ? 'OK' : 'ATTENTION'
        ];

        // Vérification des cotisations
        $cotisationsSansMontant = Cotisation::where('montant_cotisation', '<=', 0)->count();
        $cotisationsOrphelines = $this->detecterCotisationsOrphelines();
        
        $resultats['cotisations'] = [
            'total' => Cotisation::count(),
            'sans_montant' => $cotisationsSansMontant,
            'orphelines' => $cotisationsOrphelines,
            'statut' => ($cotisationsSansMontant === 0 && $cotisationsOrphelines === 0) ? 'OK' : 'ATTENTION'
        ];

        // Vérification des pré-liquidations
        $dossiersInconsistants = $this->detecterDossiersInconsistants();
        
        $resultats['preliquidations'] = [
            'total' => PreLiquidation::count(),
            'inconsistants' => $dossiersInconsistants,
            'statut' => ($dossiersInconsistants === 0) ? 'OK' : 'ATTENTION'
        ];

        return $resultats;
    }

    /**
     * Import data from file (placeholder)
     */
    public function importerDonnees(string $type, UploadedFile $fichier): array
    {
        // TODO: Implémenter l'import réel avec PhpSpreadsheet
        return [
            'type' => $type,
            'fichier' => $fichier->getClientOriginalName(),
            'lignes_traitees' => 0,
            'lignes_importees' => 0,
            'erreurs' => []
        ];
    }

    /**
     * Export data (placeholder)
     */
    public function exporterDonnees(string $type, string $format)
    {
        // TODO: Implémenter l'export réel
        return response()->json(['message' => 'Export en cours de développement']);
    }

    // Méthodes privées
    private function getDetailsCalcul(string $type, float $duree, float $taux): string
    {
        $details = "Calcul basé sur {$duree} années de cotisation. ";
        
        switch ($type) {
            case 'anticipee':
                $details .= "Pension anticipée avec coefficient de minoration de 5%.";
                break;
            case 'invalidite':
                $details .= "Pension d'invalidité avec taux minimum garanti de 50%.";
                break;
            case 'survivant':
                $details .= "Pension de survivant à 60% de la pension de référence.";
                break;
            default:
                $details .= "Pension de vieillesse normale.";
                break;
        }
        
        return $details;
    }

    private function compterAdherentsValides(): int
    {
        return Adherent::whereNotNull('numero_adherent')
                      ->whereNotNull('nom')
                      ->whereNotNull('prenoms')
                      ->count();
    }

    private function compterCotisationsCoherentes(): int
    {
        return Cotisation::where('montant_cotisation', '>', 0)
                        ->whereNotNull('adherent_id')
                        ->count();
    }

    private function compterDossiersComplets(): int
    {
        return PreLiquidation::whereNotNull('nom_beneficiaire')
                            ->whereNotNull('date_naissance')
                            ->whereNotNull('salaire_reference')
                            ->count();
    }

    private function compterDeclarationsConformes(): int
    {
        try {
            return Declaration::where('montant_declare', '>', 0)
                             ->whereNotNull('adherent_id')
                             ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getStatutGlobalVerification(): string
    {
        $stats = [
            $this->compterAdherentsValides(),
            $this->compterCotisationsCoherentes(),
            $this->compterDossiersComplets()
        ];
        
        $total = array_sum($stats);
        return $total > 10 ? 'Bon' : ($total > 5 ? 'Moyen' : 'À améliorer');
    }

    private function detecterDoublonsAdherents(): int
    {
        return Adherent::select('email')
                      ->whereNotNull('email')
                      ->groupBy('email')
                      ->havingRaw('COUNT(*) > 1')
                      ->count();
    }

    private function detecterCotisationsOrphelines(): int
    {
        return Cotisation::whereNotExists(function ($query) {
            $query->select(DB::raw(1))
                  ->from('adherents')
                  ->whereColumn('adherents.id', 'cotisations.adherent_id');
        })->count();
    }

    private function detecterDossiersInconsistants(): int
    {
        return PreLiquidation::where(function ($query) {
            $query->whereNull('salaire_reference')
                  ->orWhere('salaire_reference', '<=', 0)
                  ->orWhereNull('duree_cotisation_mois')
                  ->orWhere('duree_cotisation_mois', '<=', 0);
        })->count();
    }
}

<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;

class SettingsHelper
{
    /**
     * Get a setting value with default fallback
     */
    public static function get(string $key, $default = null)
    {
        return Cache::get("settings.{$key}", $default);
    }

    /**
     * Set a setting value
     */
    public static function set(string $key, $value): void
    {
        Cache::forever("settings.{$key}", $value);
    }

    /**
     * Get all settings
     */
    public static function all(): array
    {
        return [
            'app_name' => self::get('app_name', config('app.name', 'CRFM')),
            'app_description' => self::get('app_description', 'Système de gestion des retraites'),
            'organization_name' => self::get('organization_name', 'Caisse de Retraite des Fonctionnaires du Mali'),
            'organization_address' => self::get('organization_address', ''),
            'organization_phone' => self::get('organization_phone', ''),
            'organization_email' => self::get('organization_email', ''),
            'organization_website' => self::get('organization_website', ''),
            'logo' => self::get('logo', ''),
            'favicon' => self::get('favicon', ''),
            'timezone' => self::get('timezone', 'Africa/Bamako'),
            'date_format' => self::get('date_format', 'd/m/Y'),
            'currency' => self::get('currency', 'FCFA'),
            'language' => self::get('language', 'fr'),
            'items_per_page' => self::get('items_per_page', 15),
            'session_timeout' => self::get('session_timeout', 120),
            'backup_frequency' => self::get('backup_frequency', 'weekly'),
            'maintenance_mode' => self::get('maintenance_mode', false),
            'registration_enabled' => self::get('registration_enabled', false),
            'email_notifications' => self::get('email_notifications', true),
            'sms_notifications' => self::get('sms_notifications', false),
        ];
    }

    /**
     * Get application name
     */
    public static function appName(): string
    {
        return self::get('app_name', config('app.name', 'CRFM'));
    }

    /**
     * Get organization name
     */
    public static function organizationName(): string
    {
        return self::get('organization_name', 'Caisse de Retraite des Fonctionnaires du Mali');
    }

    /**
     * Get currency symbol
     */
    public static function currency(): string
    {
        return self::get('currency', 'FCFA');
    }

    /**
     * Get date format
     */
    public static function dateFormat(): string
    {
        return self::get('date_format', 'd/m/Y');
    }

    /**
     * Check if maintenance mode is enabled
     */
    public static function isMaintenanceMode(): bool
    {
        return self::get('maintenance_mode', false);
    }

    /**
     * Check if registration is enabled
     */
    public static function isRegistrationEnabled(): bool
    {
        return self::get('registration_enabled', false);
    }

    /**
     * Get items per page for pagination
     */
    public static function itemsPerPage(): int
    {
        return self::get('items_per_page', 15);
    }

    /**
     * Get session timeout in minutes
     */
    public static function sessionTimeout(): int
    {
        return self::get('session_timeout', 120);
    }

    /**
     * Format currency amount
     */
    public static function formatCurrency(float $amount): string
    {
        $currency = self::currency();
        return number_format($amount, 0, ',', ' ') . ' ' . $currency;
    }

    /**
     * Format date according to system settings
     */
    public static function formatDate($date): string
    {
        if (!$date) return '';
        
        $format = self::dateFormat();
        
        if (is_string($date)) {
            $date = \Carbon\Carbon::parse($date);
        }
        
        return $date->format($format);
    }
}
